
// Auto-generated by PolicyGen.py – DO NOT EDIT.
#pragma once
#include <cstddef>
constexpr uint32_t POLICY_VERSION = 1007921715u;
constexpr unsigned char kPolicyKey[32] = { 0x91,0x43,0x94,0x27,0x19,0x63,0xA0,0x52,0xF8,0x2A,0x28,0x5E,0x41,0x1A,0x2D,0xB9,0xEA,0x3E,0x6D,0x0D,0x5F,0x46,0x83,0x70,0x94,0x00,0x44,0x1E,0xA1,0xE6,0x49,0xB9 };
constexpr unsigned char kPolicyIV[16] = { 0xAE,0x28,0x0F,0x77,0x8A,0x84,0x33,0x59,0x00,0x59,0x62,0x92,0xB3,0xE0,0xAB,0x9A };
constexpr unsigned char kPolicyHmac[32] = { 0x6C,0x1A,0x94,0xFB,0xA1,0x96,0xB2,0x13,0x43,0x9C,0x04,0x55,0x53,0x3F,0x0F,0x65,0x2E,0x7D,0x4F,0x54,0x49,0x7B,0x79,0xDB,0xCA,0x52,0xB5,0x68,0x41,0x5A,0xE6,0x81 };
constexpr unsigned char kPolicyBlob[] = { 0x33,0x65,0x3F,0xEC,0x09,0xB0,0x60,0x2E,0x7E,0xD8,0xCD,0xAE,0x16,0x17,0x4C,0x95,0xBC,0x14,0x61,0x55,0x15,0x30,0x39,0x41,0x03,0x64,0xB8,0x32,0xA6,0xA2,0x0E,0x2B,0xEE,0xEF,0x2F,0x92,0x73,0x0F,0x33,0x98,0x99,0x8E,0x8D,0x79,0xC3,0x69,0x1E,0xCB };
constexpr size_t kPolicyBlobSize = sizeof(kPolicyBlob);
