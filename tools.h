﻿#pragma once
#include <string>
#include "halvault_api.h"
namespace Tools
    {
    // 将字符串转换为宽字符串，默认使用 UTF-8 编码
    // 将字节字符串转换为宽字符串
    std::wstring StringToWString( const std::string& src , UINT codePage = CP_ACP );
    // 将宽字符串转换为字节字符串
    std::string  WStringToString( const std::wstring& src , UINT codePage = CP_ACP );

    // 计算文件 MD5
    // 小于等于 10MiB: 计算整文件 MD5
    // 大于 10MiB: 读取首 4KB + 中间 4KB + 尾部 4KB，并追加最后写入时间 (FILETIME 8 字节) 后计算 MD5
    // 成功返回 32 位大写十六进制字符串；失败返回空字符串
    HALVAULT_API std::wstring MD5OfFile(const std::wstring& path);
    }