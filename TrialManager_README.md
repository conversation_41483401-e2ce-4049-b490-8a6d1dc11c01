# HALVault 试用期管理模块

## 概述

HALVault试用期管理模块是一个安全、稳定的试用期控制系统，支持天数和运行次数两种限制模式，兼容Windows 7及以上系统的普通用户权限，支持本机和移动存储设备存储。

## 主要特性

- **双重限制模式**：支持按天数和按运行次数限制
- **安全防护**：
  - 机器指纹绑定，防止复制到其他机器
  - 反调试检测
  - 时钟回拨检测和冻结机制
  - 数据加密存储
- **多副本存储**：注册表、文件、ADS（备用数据流）三重备份
- **兼容性**：支持Windows 7+，普通用户权限
- **移动存储**：支持存储到移动设备（U盘等）

## 快速开始

### 基本用法

```cpp
#include "TrialManager.hpp"

// 1. 初始化试用期（14天）
int result = TrialInitialize("MyApp-2025", TrialMode::Days, 14);
if (result != HAL_OK) {
    // 处理初始化失败
    return;
}

// 2. 查询剩余时间
uint32_t remaining = 0, total = 0;
result = TrialEvaluate(&remaining, &total);
if (result == HAL_OK) {
    printf("剩余天数: %u / %u\n", remaining, total);
} else if (result == HAL_E_TRIAL_EXPIRED) {
    printf("试用期已过期\n");
}

// 3. 按次数模式（可选）
result = TrialInitialize("MyApp-Runs-2025", TrialMode::Runs, 30);
if (result == HAL_OK) {
    // 每次使用时消耗一次
    result = TrialConsumeOneRun(&remaining, &total);
    if (result == HAL_OK) {
        printf("剩余次数: %u / %u\n", remaining, total);
    }
}
```

### C++包装类用法

```cpp
#include "TrialManager.hpp"

trial::Manager manager;

// 初始化
if (manager.initialize("MyApp-2025", TrialMode::Days, 14)) {
    // 获取详细状态
    TrialState state;
    if (manager.getState(state)) {
        printf("剩余: %u, 总数: %u, 模式: %s\n", 
               state.remaining, state.totalLimit,
               state.mode == TrialMode::Days ? "天数" : "次数");
    }
    
    // 评估状态
    uint32_t remaining, total;
    if (manager.evaluate(&remaining, &total)) {
        printf("评估结果: %u / %u\n", remaining, total);
    }
} else {
    printf("初始化失败，错误码: 0x%x\n", manager.getLastError());
}
```

## API参考

### 核心函数

#### `TrialInitialize`
```cpp
int TrialInitialize(const char* softwareId, TrialMode mode, uint32_t limit);
```
初始化试用期管理系统。

**参数：**
- `softwareId`: 软件唯一标识符
- `mode`: 试用模式（`TrialMode::Days` 或 `TrialMode::Runs`）
- `limit`: 试用限制（天数或次数）

**返回值：**
- `HAL_OK`: 成功
- `HAL_ERR_INVALID_ARG`: 参数无效
- `HAL_E_TRIAL_DEBUGGER`: 检测到调试器
- 其他错误码

#### `TrialEvaluate`
```cpp
int TrialEvaluate(uint32_t* remaining, uint32_t* totalLimit);
```
查询当前剩余额度，不消耗次数。

#### `TrialConsumeOneRun`
```cpp
int TrialConsumeOneRun(uint32_t* remaining, uint32_t* totalLimit);
```
在按次模式下消耗一次额度。

#### `TrialGetDefaultConfig`
```cpp
int TrialGetDefaultConfig(const char* softwareId, TrialMode& mode, uint32_t& limit);
```
根据软件ID获取默认配置。

### 错误码

| 错误码 | 值 | 描述 |
|--------|----|----- |
| `HAL_OK` | 0x00000000 | 成功 |
| `HAL_ERR_INVALID_ARG` | 0x80070057 | 无效参数 |
| `HAL_E_TRIAL_EXPIRED` | 0xA0010001 | 试用期已过期 |
| `HAL_E_TRIAL_FROZEN` | 0xA0010002 | 试用期被冻结 |
| `HAL_E_TRIAL_IO` | 0xA0010003 | 数据读写失败 |
| `HAL_E_TRIAL_CRYPTO` | 0xA0010004 | 加密/解密失败 |
| `HAL_E_TRIAL_DEBUGGER` | 0xA0010006 | 检测到调试器 |
| `HAL_E_TRIAL_HWID_MISMATCH` | 0xA0010007 | 硬件指纹不匹配 |

## 配置和自定义

### 默认配置规则

模块根据软件ID前缀自动选择默认配置：

- `VE*`: 30次运行
- `BDL*`: 7天
- 其他: 14天

### 存储位置管理

```cpp
// 设置自定义存储根目录（用于移动存储设备）
TrialSetStorageRoot(L"E:\\MyApp\\TrialData");

// 获取当前存储根目录
wchar_t buffer[MAX_PATH];
TrialGetStorageRoot(buffer, MAX_PATH);
```

### 混合策略（高级功能）

```cpp
// 先用5次，然后转为7天模式
TrialInitialize("MyApp-2025", TrialMode::Days, 14);
TrialConfigureMixed(5, 7);
```

## 安全特性

### 机器指纹绑定
- 基于硬件ID、卷序列号、计算机名生成唯一指纹
- 防止试用期数据在不同机器间复制

### 反调试保护
- 检测调试器存在
- 时间扰动检测防止单步调试
- 检测到调试器时拒绝运行

### 时钟回拨防护
- 检测系统时间回拨（超过5分钟）
- 累计篡改次数，超过阈值时冻结试用期
- 时间恢复后自动解冻

### 数据加密
- 使用AES-256-GCM加密存储试用期数据
- 基于机器指纹和随机盐派生加密密钥
- 防止数据被直接修改

## 故障排查

### 常见问题

1. **初始化失败**
   - 检查软件ID是否有效（非空）
   - 确认没有调试器运行
   - 检查系统权限

2. **试用期被冻结**
   - 检查系统时间是否正确
   - 等待时间自然恢复到正常值
   - 避免手动调整系统时间

3. **硬件指纹不匹配**
   - 试用期数据与当前机器不匹配
   - 需要重新初始化试用期

4. **数据读写失败**
   - 检查磁盘空间和权限
   - 确认杀毒软件没有阻止文件操作
   - 检查注册表访问权限

### 诊断工具

```cpp
// 验证数据完整性
int result = TrialVerifyIntegrity("MyApp-2025");

// 修复损坏的数据
if (result != HAL_OK) {
    TrialRepairData("MyApp-2025");
}

// 获取日志信息
char logBuffer[4096];
size_t actualSize;
TrialGetLog(logBuffer, sizeof(logBuffer), &actualSize);
```

## 编译和集成

### 依赖项
- Windows SDK
- bcrypt.lib
- shlwapi.lib
- shell32.lib

### 编译选项
```cpp
// 在预处理器定义中添加
#define HALVAULT_EXPORTS  // 用于DLL导出

// 链接库
#pragma comment(lib, "bcrypt.lib")
#pragma comment(lib, "shlwapi.lib")
#pragma comment(lib, "shell32.lib")
```

### 项目集成
1. 将 `TrialManager.hpp` 和 `TrialManager.cpp` 添加到项目
2. 在需要使用的地方包含头文件：`#include "TrialManager.hpp"`
3. 调用相应的API函数

## 示例代码

完整的使用示例请参考：
- `TrialExample.cpp` - 基本使用示例
- `TrialTest.cpp` - 功能测试代码

## 许可证

本模块是HALVault项目的一部分，遵循项目的许可证条款。

## 技术支持

如有问题或建议，请联系HALVault开发团队。
