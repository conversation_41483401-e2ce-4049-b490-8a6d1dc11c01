# HALVault 集成指南

本指南详细说明如何将 HALVault 集成到您的应用程序中。

## 📋 目录

- [环境准备](#环境准备)
- [项目配置](#项目配置)
- [基础集成](#基础集成)
- [高级功能](#高级功能)
- [部署配置](#部署配置)
- [最佳实践](#最佳实践)

## 🔧 环境准备

### 系统要求

- **操作系统**: Windows 7 SP1 及以上版本
- **编译器**: Visual Studio 2019 或更高版本
- **架构**: x86 / x64
- **运行时**: Visual C++ Redistributable 2019

### 依赖库

HALVault 需要以下系统库：

```cpp
#pragma comment(lib, "bcrypt.lib")      // Windows CNG 加密
#pragma comment(lib, "winhttp.lib")     // HTTP 通信
#pragma comment(lib, "advapi32.lib")    // 注册表访问
#pragma comment(lib, "wbemuuid.lib")    // WMI 查询
#pragma comment(lib, "crypt32.lib")     // 证书验证
```

第三方依赖：
- **libcurl**: HTTP/HTTPS 网络通信
- **cryptopp**: 加密算法实现
- **VMProtect SDK** (可选): 代码保护

## ⚙️ 项目配置

### Visual Studio 项目设置

1. **C++ 标准**: 设置为 C++17 或更高
2. **字符集**: 使用 Unicode 字符集
3. **运行时库**: 
   - Debug: Multi-threaded Debug (/MTd)
   - Release: Multi-threaded (/MT)

### 预处理器定义

```cpp
// Debug 配置
WIN32;_DEBUG;_WINDOWS;_USRDLL;HALVAULT_EXPORTS

// Release 配置  
WIN32;NDEBUG;_WINDOWS;_USRDLL;HALVAULT_EXPORTS
```

### 包含目录

```
$(ProjectDir)
$(ProjectDir)HALVault
A:\VMProtectSDK\Include\C
E:\vcpkg\installed\x86-windows-static\include
```

### 库目录

```
A:\VMProtectSDK\Lib\Windows
E:\vcpkg\installed\x86-windows-static\lib
E:\vcpkg\installed\x86-windows-static\debug\lib
```

## 🚀 基础集成

### 1. 引入头文件

```cpp
#include "halvault_api.h"
#include "VaultManager.hpp"
#include "HALVault/TrialManager.hpp"
```

### 2. 初始化系统

在应用程序启动时调用：

```cpp
bool InitializeHALVault() {
    // 初始化 HALVault 核心
    int result = HALVault_Initialize(L"YourApp-v1.0", L"https://your-server.com");
    if (result != HAL_OK) {
        wprintf(L"HALVault 初始化失败: %s\n", HalGetErrorText(result));
        return false;
    }

    // 初始化 VaultManager
    VaultManager::Initialize(L"YourApp", L"YourCompany");
    
    // 初始化试用管理
    result = TrialInitialize("YourApp-001");
    if (result != HAL_OK) {
        wprintf(L"试用系统初始化失败: %s\n", HalGetErrorText(result));
        return false;
    }

    return true;
}
```

### 3. 试用检查

在关键功能入口处检查试用状态：

```cpp
bool CheckTrialStatus() {
    TrialStatus status;
    int result = TrialEvaluate(status);
    
    if (result != HAL_OK) {
        wprintf(L"试用检查失败: %s\n", HalGetErrorText(result));
        return false;
    }

    if (!status.allowed) {
        if (status.mode == TrialMode::Days) {
            wprintf(L"试用期已过，共 %d 天\n", status.limit);
        } else {
            wprintf(L"试用次数已用完，共 %d 次\n", status.limit);
        }
        return false;
    }

    // 显示剩余试用信息
    if (status.mode == TrialMode::Days) {
        wprintf(L"试用剩余: %d 天\n", status.remaining);
    } else {
        wprintf(L"试用剩余: %d 次\n", status.remaining);
    }

    return true;
}
```

### 4. 消耗试用次数

在按次数计费的功能中：

```cpp
bool ConsumeTrialRun() {
    int result = TrialConsumeOne();
    if (result != HAL_OK) {
        wprintf(L"试用次数消耗失败: %s\n", HalGetErrorText(result));
        return false;
    }
    return true;
}
```

## 🔐 高级功能

### 硬件指纹获取

```cpp
std::string GetDeviceFingerprint() {
    // 获取硬件指纹
    wchar_t hwidBuf[64];
    int result = VaultManager::GetHWID(hwidBuf, sizeof(hwidBuf)/sizeof(wchar_t));
    
    if (result == HAL_OK) {
        // 转换为 std::string
        int len = WideCharToMultiByte(CP_UTF8, 0, hwidBuf, -1, nullptr, 0, nullptr, nullptr);
        std::string hwid(len - 1, 0);
        WideCharToMultiByte(CP_UTF8, 0, hwidBuf, -1, &hwid[0], len, nullptr, nullptr);
        return hwid;
    }
    
    return "";
}
```

### 在线激活

```cpp
bool ActivateOnline(const std::string& serial, const std::string& contact) {
    std::string hwid = GetDeviceFingerprint();
    if (hwid.empty()) {
        wprintf(L"无法获取硬件指纹\n");
        return false;
    }

    wchar_t errorBuf[512];
    int httpCode;
    
    int result = VaultManager::ActivateOnline(
        L"https://your-server.com",  // 服务器地址
        serial,                      // 序列号
        contact,                     // 联系方式
        hwid,                        // 硬件指纹
        1,                           // 软件 ID
        errorBuf,                    // 错误信息缓冲区
        sizeof(errorBuf)/sizeof(wchar_t),
        httpCode                     // HTTP 状态码
    );

    if (result == HAL_OK) {
        wprintf(L"激活成功\n");
        return true;
    } else {
        wprintf(L"激活失败 (HTTP %d): %s\n", httpCode, errorBuf);
        return false;
    }
}
```

### 授权文件检查

```cpp
bool CheckLicenseFile() {
    bool hasFile, valid;
    int result = VaultManager::CheckLicense(hasFile, valid);
    
    if (result != HAL_OK) {
        wprintf(L"授权检查失败: %s\n", HalGetErrorText(result));
        return false;
    }

    if (!hasFile) {
        wprintf(L"未找到授权文件\n");
        return false;
    }

    if (!valid) {
        wprintf(L"授权文件无效或已过期\n");
        return false;
    }

    // 获取授权详细信息
    LicenseInfo info;
    result = VaultManager::GetLicenseInfo(info);
    if (result == HAL_OK) {
        time_t expireTime = info.expireUnix;
        wprintf(L"授权有效，过期时间: %s", _wctime(&expireTime));
    }

    return true;
}
```

### 安全防护

```cpp
bool InitializeSecurity() {
    // 启动反调试保护
    anti::Start();
    
    // 检查调试器
    if (anti::IsDebuggerPresentFast()) {
        wprintf(L"检测到调试器，程序退出\n");
        return false;
    }

    // 检查虚拟机（可选）
    anti::SetVmAllowed(false);  // 禁止在虚拟机中运行
    if (anti::IsRunningInVM()) {
        wprintf(L"检测到虚拟机环境，程序退出\n");
        return false;
    }

    // 验证代码完整性
    if (!anti::VerifySelfIntegrity()) {
        wprintf(L"代码完整性验证失败\n");
        return false;
    }

    return true;
}
```

## 📦 部署配置

### 文件结构

```
YourApp/
├── YourApp.exe
├── HALVault.dll
├── vcruntime140.dll      # VC++ 运行时
├── msvcp140.dll
├── config/
│   └── license.dat       # 授权文件（如果有）
└── logs/
    └── halvault.log      # 日志文件
```

### 安装程序配置

在安装程序中添加以下步骤：

1. **检查运行时**: 确保安装了 VC++ 2019 Redistributable
2. **创建目录**: 创建必要的配置和日志目录
3. **注册 DLL**: 如果需要，注册 HALVault.dll
4. **设置权限**: 确保应用程序有足够的文件和注册表访问权限

### 卸载清理

```cpp
void CleanupHALVault() {
    // 停止安全防护
    anti::Stop();
    
    // 清理临时文件（可选）
    // 注意：不要删除试用数据，除非用户明确要求
}
```

## 🎯 最佳实践

### 1. 错误处理

始终检查 API 返回值并提供友好的错误信息：

```cpp
int result = SomeHALVaultFunction();
if (result != HAL_OK) {
    int lastError = HalGetLastError();
    const wchar_t* errorText = HalGetErrorText(lastError);
    
    // 记录详细错误信息
    LogError(L"HALVault 操作失败: %s (错误码: %d)", errorText, lastError);
    
    // 向用户显示友好信息
    ShowUserMessage(L"操作失败，请稍后重试或联系技术支持");
}
```

### 2. 线程安全

HALVault API 是线程安全的，但建议在主线程中进行初始化：

```cpp
// 在主线程中初始化
std::once_flag initFlag;
std::call_once(initFlag, []() {
    InitializeHALVault();
});
```

### 3. 性能优化

- 缓存硬件指纹，避免重复计算
- 批量检查试用状态，减少 I/O 操作
- 在后台线程进行网络激活

```cpp
class HALVaultManager {
private:
    std::string cachedHWID;
    std::chrono::steady_clock::time_point lastCheck;
    
public:
    const std::string& GetHWID() {
        if (cachedHWID.empty()) {
            cachedHWID = GetDeviceFingerprint();
        }
        return cachedHWID;
    }
    
    bool ShouldCheckTrial() {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - lastCheck);
        
        if (elapsed.count() >= 5) {  // 每5分钟检查一次
            lastCheck = now;
            return true;
        }
        return false;
    }
};
```

### 4. 日志记录

实现详细的日志记录以便调试：

```cpp
void LogHALVaultEvent(const wchar_t* event, int result) {
    wchar_t timestamp[64];
    GetTimeFormatW(LOCALE_USER_DEFAULT, 0, nullptr, nullptr, timestamp, 64);
    
    wprintf(L"[%s] %s: %s (结果: %d)\n", 
            timestamp, event, 
            result == HAL_OK ? L"成功" : HalGetErrorText(result), 
            result);
}
```

### 5. 用户体验

提供清晰的试用状态提示：

```cpp
void ShowTrialStatus(const TrialStatus& status) {
    if (status.allowed) {
        if (status.mode == TrialMode::Days) {
            if (status.remaining <= 3) {
                ShowWarning(L"试用期即将结束，剩余 %d 天", status.remaining);
            } else {
                ShowInfo(L"试用期剩余 %d 天", status.remaining);
            }
        } else {
            if (status.remaining <= 5) {
                ShowWarning(L"试用次数即将用完，剩余 %d 次", status.remaining);
            } else {
                ShowInfo(L"试用次数剩余 %d 次", status.remaining);
            }
        }
    } else {
        ShowError(L"试用期已结束，请购买正式版本");
    }
}
```

---

**注意**: 在生产环境中，建议对 HALVault.dll 进行代码混淆和加壳保护，以提高安全性。
