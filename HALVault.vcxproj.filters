﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="framework.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="pch.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="activate.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Base32.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="hwid.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="license.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SehException.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SerialValidator.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="VaultManager.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="AntiGuard.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SoftwareCatalog.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="halvault_api.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="tools.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\VMProtectSDK\Include\C\ScopeGuard.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="pch.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="activate.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="hwid.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="license.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SerialValidator.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="halvault_error.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="VaultManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="AntiGuard.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SoftwareCatalog.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="tools.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="init.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="HALVault.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>