#include "pch.h"
#include "trialmanager.hpp"
#include <iostream>
#include <iomanip>

void PrintTrialStatus(const TrialStatus& st) {
    std::cout << "  允许试用: " << (st.allowed ? "是" : "否") << std::endl;
    std::cout << "  试用模式: " << (st.mode == TrialMode::Days ? "按天" : "按次") << std::endl;
    std::cout << "  剩余配额: " << st.remaining << std::endl;
    std::cout << "  总限制: " << st.limit << std::endl;
    std::cout << "  已用次数: " << st.runsUsed << std::endl;
    std::cout << "  安装时间: " << st.installUnix << std::endl;
    std::cout << "  当前时间: " << st.nowUnix << std::endl;
}

int main() {
    std::cout << "=== 新API接口测试 ===" << std::endl;
    
    // 测试1: 初始化
    std::cout << "\n1. 初始化试用系统:" << std::endl;
    int rc = halvault::Initialize("MCE0012");
    std::cout << "  返回值: " << rc << " (" << (rc == 0 ? "成功" : "失败") << ")" << std::endl;
    
    // 测试2: 获取配置
    std::cout << "\n2. 获取配置信息:" << std::endl;
    TrialMode mode;
    uint32_t limit;
    rc = halvault::GetConfig("MCE0012", mode, limit);
    std::cout << "  返回值: " << rc << std::endl;
    std::cout << "  配置模式: " << (mode == TrialMode::Days ? "按天" : "按次") << std::endl;
    std::cout << "  配置限制: " << limit << std::endl;
    
    // 测试3: 获取完整状态
    std::cout << "\n3. 获取试用状态:" << std::endl;
    TrialStatus status;
    rc = halvault::GetStatus(status);
    std::cout << "  返回值: " << rc << " (" << (rc == 0 ? "成功" : "失败") << ")" << std::endl;
    if (rc == 0) {
        PrintTrialStatus(status);
    }
    
    // 测试4: 便利函数
    std::cout << "\n4. 便利函数测试:" << std::endl;
    bool valid = halvault::IsTrialValid();
    uint32_t quota = halvault::GetRemainingQuota();
    std::cout << "  试用有效: " << (valid ? "是" : "否") << std::endl;
    std::cout << "  剩余配额: " << quota << std::endl;
    
    // 测试5: 消耗运行（仅在Runs模式有效）
    if (status.mode == TrialMode::Runs && status.remaining > 0) {
        std::cout << "\n5. 消耗一次运行:" << std::endl;
        TrialStatus newStatus;
        rc = halvault::ConsumeOneRun(newStatus);
        std::cout << "  返回值: " << rc << std::endl;
        if (rc == 0) {
            std::cout << "  消耗前剩余: " << status.remaining << std::endl;
            std::cout << "  消耗后剩余: " << newStatus.remaining << std::endl;
            std::cout << "  变化: " << (int)status.remaining - (int)newStatus.remaining << std::endl;
        }
    } else {
        std::cout << "\n5. 跳过运行消耗测试 (不适用于当前模式或已耗尽)" << std::endl;
    }
    
    // 测试6: 验证配置是否正确
    std::cout << "\n6. MCE0012配置验证:" << std::endl;
    bool configCorrect = (mode == TrialMode::Runs && limit == 30);
    std::cout << "  期望: Runs模式, 30次限制" << std::endl;
    std::cout << "  实际: " << (mode == TrialMode::Days ? "Days" : "Runs") 
              << "模式, " << limit << "次限制" << std::endl;
    std::cout << "  结果: " << (configCorrect ? "✅ 配置正确" : "❌ 配置错误") << std::endl;
    
    return 0;
}
