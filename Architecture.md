# HALVault 架构设计文档

本文档详细描述了 HALVault 系统的架构设计、组件关系和数据流程。

## 🏗️ 系统架构概览

### 整体架构图

```mermaid
graph TB
    subgraph "应用层"
        A[用户应用程序]
        B[MFC/Win32 GUI]
        C[控制台应用]
    end
    
    subgraph "HALVault API 层"
        D[halvault_api.h]
        E[VaultManager 统一接口]
    end
    
    subgraph "核心功能模块"
        F[试用管理<br/>TrialManager]
        G[授权管理<br/>License]
        H[在线激活<br/>Activate]
        I[硬件指纹<br/>HWID]
        J[序列号验证<br/>SerialValidator]
        K[安全防护<br/>AntiGuard]
        L[软件目录<br/>SoftwareCatalog]
    end
    
    subgraph "存储层"
        M[注册表存储]
        N[文件系统存储]
        O[NTFS ADS 存储]
    end
    
    subgraph "加密层"
        P[AES-256-GCM]
        Q[HKDF-SHA256]
        R[HMAC-SHA256]
    end
    
    subgraph "网络层"
        S[WinHTTP]
        T[libcurl]
        U[SSL/TLS]
    end
    
    subgraph "系统层"
        V[Windows CNG]
        W[WMI 查询]
        X[设备管理器]
        Y[文件系统 API]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H
    E --> I
    E --> J
    E --> K
    E --> L
    
    F --> M
    F --> N
    F --> O
    G --> P
    H --> S
    H --> T
    I --> W
    I --> X
    
    P --> V
    Q --> V
    R --> V
    S --> U
    T --> U
    
    M --> Y
    N --> Y
    O --> Y
```

## 🔧 核心模块设计

### 1. VaultManager - 统一管理接口

```mermaid
classDiagram
    class VaultManager {
        -static wstring s_companyName
        -static wstring s_projectName
        -static wstring s_licensePath
        -static once_flag s_initFlag
        -static mutex s_pathMutex
        
        +static Initialize(projectName, companyName)
        +static SetLicenseFilePath(fullPath)
        +static GetLastError()
        +static GetErrorText(code)
        +static GetHWID(hwidHexBuf, bufLen)
        +static CheckLicense(hasFile, valid)
        +static LicenseSecondsRemaining(seconds)
        +static VerifySerial(serial, contact, info)
        +static ActivateOnline(serverUrl, serial, contact, hwid, softwareId, error, bufLen, httpCode, ignoreCert)
        +static GetLicenseInfo(info)
        +static GetLicenseInfoC(info)
        +static GetLicenseExpiryDetails(expireUnix, details)
        -static ResolveLicensePath()
    }
```

### 2. TrialManager - 试用管理

```mermaid
stateDiagram-v2
    [*] --> 未初始化
    未初始化 --> 初始化中 : TrialInitialize()
    初始化中 --> 试用有效 : 首次安装/数据有效
    初始化中 --> 试用过期 : 超过限制
    初始化中 --> 试用冻结 : 检测到篡改
    
    试用有效 --> 试用有效 : TrialEvaluate()
    试用有效 --> 试用过期 : 达到限制
    试用有效 --> 试用冻结 : 时间回拨
    
    试用过期 --> [*]
    试用冻结 --> 试用有效 : 时间恢复
    试用冻结 --> [*] : 超过冻结阈值
```

### 3. 硬件指纹生成流程

```mermaid
flowchart TD
    A[开始生成 HWID] --> B[获取 CPU 信息]
    B --> C[获取主板序列号]
    C --> D[获取硬盘序列号]
    D --> E[获取系统信息]
    E --> F[组合所有信息]
    F --> G[SHA-256 哈希]
    G --> H[截取前 16 字节]
    H --> I[返回 HWID]
    
    D --> J{硬盘序列号获取失败?}
    J -->|是| K[使用卷序列号备用]
    J -->|否| F
    K --> F
```

### 4. 试用数据存储架构

```mermaid
graph LR
    subgraph "存储副本"
        A[注册表副本<br/>HKCU\Software\HALVault]
        B[本地文件副本<br/>%LocalAppData%\HALVault]
        C[漫游文件副本<br/>%AppData%\HALVault]
        D[ADS 副本<br/>trial.bin:alt]
        E[注册表备用副本<br/>HKCU\...\alt]
        F[ADS 备用副本<br/>trial.bin:meta]
    end
    
    subgraph "数据验证"
        G[多数投票算法]
        H[HMAC 完整性校验]
        I[时间戳验证]
    end
    
    A --> G
    B --> G
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> H
    H --> I
    I --> J[选择有效数据]
```

## 🔐 安全架构设计

### 加密数据流程

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant TM as TrialManager
    participant Crypto as 加密模块
    participant Storage as 存储层
    
    App->>TM: TrialInitialize()
    TM->>Crypto: 生成随机盐
    TM->>Crypto: HKDF 密钥派生
    Note over Crypto: 密钥 = HKDF(APP_SECRET + HWID + salt)
    TM->>Crypto: AES-GCM 加密试用数据
    TM->>Storage: 保存到多个副本
    Storage-->>TM: 确认保存
    TM-->>App: 初始化完成
    
    App->>TM: TrialEvaluate()
    TM->>Storage: 读取所有副本
    Storage-->>TM: 返回加密数据
    TM->>Crypto: AES-GCM 解密
    TM->>TM: 多数投票验证
    TM-->>App: 返回试用状态
```

### 反调试保护流程

```mermaid
flowchart TD
    A[启动反调试保护] --> B[检查 IsDebuggerPresent]
    B --> C{发现调试器?}
    C -->|是| D[记录安全事件]
    C -->|否| E[检查远程调试器]
    
    E --> F{发现远程调试?}
    F -->|是| D
    F -->|否| G[时间扰动检测]
    
    G --> H{检测到单步调试?}
    H -->|是| D
    H -->|否| I[虚拟机检测]
    
    I --> J{运行在虚拟机?}
    J -->|是且不允许| D
    J -->|否或允许| K[代码完整性检查]
    
    K --> L{代码被篡改?}
    L -->|是| D
    L -->|否| M[继续正常执行]
    
    D --> N[触发保护措施]
    N --> O[退出程序]
```

## 📊 数据模型设计

### 试用数据结构

```mermaid
erDiagram
    TrialData {
        uint32_t schema_version
        uint64_t install_time
        uint64_t expire_time
        uint64_t last_run_time
        uint32_t run_count
        uint32_t max_runs
        uint32_t tamper_count
        uint8_t machine_hash[32]
        uint8_t salt[16]
        string app_id
        string version
        uint32_t sequence_number
    }
    
    LogEntry {
        uint64_t timestamp
        uint32_t sequence_number
        uint32_t max_seen_time
        uint32_t days_used
        uint8_t hmac[16]
    }
    
    TrialData ||--o{ LogEntry : "generates"
```

### 授权数据结构

```mermaid
erDiagram
    LicenseInfo {
        uint8_t hwid[16]
        string contact
        uint8_t software_id
        string serial
        uint32_t expire_unix
        uint32_t issued_at
        uint8_t payload_version
        uint16_t quantity
        char serial_prefix[8]
        string license_str
    }
    
    SerialInfo {
        uint8_t version
        uint8_t software_id
        uint32_t expire_unix
        uint8_t contact_hash[3]
    }
    
    LicenseInfo ||--|| SerialInfo : "contains"
```

## 🌐 网络通信架构

### 在线激活流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant HAL as HALVault
    participant Server as 授权服务器
    participant DB as 数据库
    
    Client->>HAL: RequestLicense(serial, hwid, contact)
    HAL->>HAL: 验证参数格式
    HAL->>Server: POST /api/activate
    Note over HAL,Server: JSON: {serial, hwid, contact, software_id}
    
    Server->>DB: 查询序列号状态
    DB-->>Server: 返回授权信息
    Server->>Server: 验证硬件绑定
    Server->>Server: 生成授权数据
    Server-->>HAL: 返回授权响应
    Note over Server,HAL: JSON: {license_data, quantity, expire_time}
    
    HAL->>HAL: 验证响应签名
    HAL->>HAL: 保存授权文件
    HAL-->>Client: 返回激活结果
```

### 授权检查流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant HAL as HALVault
    participant Server as 授权服务器
    
    Client->>HAL: CheckLicense(serial, hwid)
    HAL->>Server: POST /api/check
    Server->>Server: 验证授权状态
    Server-->>HAL: 返回状态信息
    HAL->>HAL: 更新本地状态
    HAL-->>Client: 返回检查结果
```

## 🔄 状态管理

### 试用状态转换

```mermaid
stateDiagram-v2
    [*] --> NotInitialized
    NotInitialized --> Initializing : TrialInitialize()
    
    Initializing --> FirstInstall : 首次安装
    Initializing --> DataCorrupted : 数据损坏
    Initializing --> ValidTrial : 数据有效
    
    FirstInstall --> ValidTrial : 创建试用数据
    DataCorrupted --> ValidTrial : 数据修复成功
    DataCorrupted --> TrialExpired : 修复失败
    
    ValidTrial --> ValidTrial : TrialEvaluate() [未过期]
    ValidTrial --> TrialExpired : 达到时间/次数限制
    ValidTrial --> TrialFrozen : 检测到时间回拨
    
    TrialFrozen --> ValidTrial : 时间恢复正常
    TrialFrozen --> TrialExpired : 冻结次数过多
    
    TrialExpired --> [*]
```

### 授权状态管理

```mermaid
stateDiagram-v2
    [*] --> NoLicense
    NoLicense --> Activating : 开始激活
    NoLicense --> LoadingLicense : 加载本地授权
    
    Activating --> Licensed : 激活成功
    Activating --> ActivationFailed : 激活失败
    
    LoadingLicense --> Licensed : 授权有效
    LoadingLicense --> LicenseExpired : 授权过期
    LoadingLicense --> LicenseInvalid : 授权无效
    
    Licensed --> Licensed : 定期检查 [有效]
    Licensed --> LicenseExpired : 授权过期
    Licensed --> LicenseInvalid : 硬件不匹配
    
    ActivationFailed --> NoLicense : 重试
    LicenseExpired --> NoLicense : 清理过期授权
    LicenseInvalid --> NoLicense : 清理无效授权
```

## 📈 性能优化策略

### 缓存机制

```mermaid
graph TD
    A[API 调用] --> B{缓存命中?}
    B -->|是| C[返回缓存结果]
    B -->|否| D[执行实际操作]
    D --> E[更新缓存]
    E --> F[返回结果]
    
    G[缓存失效策略] --> H[时间过期]
    G --> I[数据变更]
    G --> J[手动清理]
    
    H --> K[清理过期缓存]
    I --> K
    J --> K
```

### 并发控制

```mermaid
sequenceDiagram
    participant T1 as 线程1
    participant T2 as 线程2
    participant Mutex as 进程内互斥锁
    participant NamedMutex as 跨进程互斥锁
    participant Storage as 存储层
    
    T1->>Mutex: 获取锁
    T2->>Mutex: 等待锁
    T1->>NamedMutex: 获取跨进程锁
    T1->>Storage: 读写操作
    Storage-->>T1: 操作完成
    T1->>NamedMutex: 释放跨进程锁
    T1->>Mutex: 释放锁
    T2->>Mutex: 获取锁成功
```

---

**注意**: 本架构设计文档反映了 HALVault 的当前实现状态，随着功能的扩展和优化，架构可能会有所调整。
