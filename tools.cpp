﻿#include "pch.h"
#include "tools.h"
#include "AntiGuard.hpp"
#include <ScopeGuard.h>
#include <vector>
#include <cstdio>
#include <iomanip>
#include <sstream>
#include <limits>
#include "halvault_error.h"

// Crypto++
#include <cryptopp/md5.h>
#include <cryptopp/filters.h>
#include <cryptopp/hex.h>

using namespace CryptoPP;

namespace Tools
    {
    std::wstring StringToWString( const std::string& src , UINT codePage )
        {
        if (src.empty( )) return {};

        // 使用MultiByteToWideChar进行转换，处理混合字符集
        int len = ::MultiByteToWideChar( codePage , 0 , src.data( ) , static_cast< int >( src.size( ) ) , nullptr , 0 );
        if (len <= 0) return {};

        std::wstring wide( len , L'\0' );
        ::MultiByteToWideChar( codePage , 0 , src.data( ) , static_cast< int >( src.size( ) ) , &wide [ 0 ] , len );
        return wide;
        }

    std::string  WStringToString( const std::wstring& src , UINT codePage )
        {
        if (src.empty( )) return {};

        // 使用WideCharToMultiByte进行转换，处理混合字符集
        int len = ::WideCharToMultiByte( codePage , 0 , src.data( ) , static_cast< int >( src.size( ) ) , nullptr , 0 , nullptr , nullptr );
        if (len <= 0) return {};

        std::string narrow( len , '\0' );
        ::WideCharToMultiByte( codePage , 0 , src.data( ) , static_cast< int >( src.size( ) ) , &narrow [ 0 ] , len , nullptr , nullptr );
        return narrow;
        }

    // 计算文件 MD5（支持超大文件采样 & 64 位偏移）
    std::wstring MD5OfFile(const std::wstring& path)
        {
        HANDLE hFile = ::CreateFileW(path.c_str(), GENERIC_READ, FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
                                      nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
        if (hFile == INVALID_HANDLE_VALUE) {
            return {};
        }

        LARGE_INTEGER fileSize{};
        if (!::GetFileSizeEx(hFile, &fileSize)) {
            ::CloseHandle(hFile);
            return {};
        }

        FILETIME ftWrite{};
        if (!::GetFileTime(hFile, nullptr, nullptr, &ftWrite)) {
            ::CloseHandle(hFile);
            return {};
        }

        const ULONGLONG size = static_cast<ULONGLONG>(fileSize.QuadPart);
        const ULONGLONG threshold = 10ull * 1024ull * 1024ull; // 10MiB
        const DWORD chunk = 4096; // 4KB

        Weak1::MD5 md5;

        auto feedRange = [&](ULONGLONG offset, DWORD bytes) -> bool {
            LARGE_INTEGER li{}; li.QuadPart = static_cast<LONGLONG>(offset);
            if (!::SetFilePointerEx(hFile, li, nullptr, FILE_BEGIN)) return false;
            std::vector<byte> buf(bytes);
            DWORD read = 0; if (!::ReadFile(hFile, buf.data(), bytes, &read, nullptr)) return false;
            if (read > 0) md5.Update(buf.data(), read);
            return true;
        };

        bool ok = true;
        if (size <= threshold) {
            // 全量读取（分块以限制内存）
            const DWORD bufSize = 1u << 20; // 1 MiB
            std::vector<byte> buf(bufSize);
            LARGE_INTEGER li{}; li.QuadPart = 0;
            if (!::SetFilePointerEx(hFile, li, nullptr, FILE_BEGIN)) { ::CloseHandle(hFile); return {}; }
            for (;;) {
                DWORD read = 0;
                if (!::ReadFile(hFile, buf.data(), bufSize, &read, nullptr)) { ok = false; break; }
                if (read == 0) break;
                md5.Update(buf.data(), read);
            }
        } else {
            // 采样三段：首 4KB，中间 4KB，尾 4KB
            if (size > 0) ok = ok && feedRange(0ull, chunk);

            if (size > chunk) {
                ULONGLONG midStart = (size >= 2ull * chunk) ? (size / 2ull - (chunk / 2ull)) : 0ull;
                // 保证不越尾
                if (midStart + chunk > size) midStart = (size > chunk) ? (size - chunk) : 0ull;
                ok = ok && feedRange(midStart, chunk);
            }

            if (size >= chunk) ok = ok && feedRange(size - chunk, chunk);

            // 追加最后写入时间（8 字节，FILETIME 小端）
            const byte* p = reinterpret_cast<const byte*>(&ftWrite);
            md5.Update(p, sizeof(ftWrite));
        }

        ::CloseHandle(hFile);
        if (!ok) return {};

        byte digest[Weak1::MD5::DIGESTSIZE]{};
        md5.Final(digest);

        // 转为大写十六进制（宽字符串）
        wchar_t hex[] = L"0123456789ABCDEF";
        std::wstring out;
        out.resize(Weak1::MD5::DIGESTSIZE * 2);
        for (size_t i = 0; i < Weak1::MD5::DIGESTSIZE; ++i) {
            out[i * 2 + 0] = hex[(digest[i] >> 4) & 0xF];
            out[i * 2 + 1] = hex[digest[i] & 0xF];
        }
        return out;
        }

    } // namespace Tools

extern "C" HALVAULT_API int HALVault_GetFileMD5(const wchar_t* pathOrNull, wchar_t* outMd5, size_t outMd5Chars) noexcept
{
    if (outMd5 == nullptr || outMd5Chars == 0) {
        HalSetLastError(HAL_ERR_INVALID_ARG);
        return 0;
    }

    std::wstring path;
    if (pathOrNull == nullptr) {
        // 获取自身 DLL 路径
        HMODULE hMod = nullptr;
        if (!::GetModuleHandleExW(GET_MODULE_HANDLE_EX_FLAG_FROM_ADDRESS,
                                  reinterpret_cast<LPCWSTR>(&HALVault_GetFileMD5), &hMod)) {
            HalSetLastError(HAL_ERR_UNKNOWN);
            return 0;
        }
        wchar_t buf[MAX_PATH];
        DWORD len = ::GetModuleFileNameW(hMod, buf, static_cast<DWORD>(sizeof(buf) / sizeof(buf[0])));
        if (len == 0) {
            HalSetLastError(HAL_ERR_UNKNOWN);
            return 0;
        }
        path.assign(buf, len);
    } else {
        path = pathOrNull;
    }

    std::wstring md5 = Tools::MD5OfFile(path);
    if (md5.empty()) {
        HalSetLastError(HAL_ERR_FILE_NOT_FOUND);
        return 0;
    }

    // 需要 32 字符 + 终止符
    if (outMd5Chars < 33) {
        HalSetLastError(HAL_ERR_INVALID_ARG);
        return 0;
    }

    // 拷贝结果
    wcsncpy_s(outMd5, outMd5Chars, md5.c_str(), _TRUNCATE);
    HalSetLastError(HAL_OK);
    return 1;
}

extern "C" HALVAULT_API int HALVault_VerificationFile(const wchar_t* inMd5) noexcept
{
    VMP_GUARD( "VerifySerial" );
    // 1) 参数校验
    if (inMd5 == nullptr || wcslen(inMd5) != 32) {
        HalSetLastError(HAL_ERR_INVALID_ARG);
        return 0;
    }

    // 2) 取自身 MD5
    wchar_t md5[33] = {};
    if (HALVault_GetFileMD5(nullptr, md5, 33) == 0) {
        // HalSetLastError 已在内部设置
        return 0;
    }

    // 3) 大小写无关比较（你的 MD5 是大写，这里兼容调用方大小写）
    if (_wcsicmp(md5, inMd5) != 0) {
        // 上报后再终止
        anti::ReportAntiEventInternal( __FUNCTION__ , __LINE__ , "verificationFile" );
        // 建议：改为 return 0 由上层决定是否退出
        ::TerminateProcess( GetCurrentProcess( ) , -1 );
    }

    HalSetLastError(HAL_OK);
    return 1;
}