// TrialTest.cpp - 试用期管理模块的基本测试
// 这个文件包含了对TrialManager模块的基本功能测试

#include "pch.h"
#include "TrialManager.hpp"
#include "halvault_error.h"
#include <cassert>
#include <iostream>

// 测试辅助宏
#define TEST_ASSERT(condition, message) \
    do { \
        if (!(condition)) { \
            std::wcout << L"测试失败: " << message << L" (行 " << __LINE__ << L")" << std::endl; \
            return false; \
        } \
    } while(0)

#define TEST_EXPECT_ERROR(call, expected_error, message) \
    do { \
        int result = (call); \
        if (result != (expected_error)) { \
            std::wcout << L"测试失败: " << message << L" - 期望错误码 0x" << std::hex << (expected_error) \
                       << L", 实际错误码 0x" << std::hex << result << L" (行 " << __LINE__ << L")" << std::endl; \
            return false; \
        } \
    } while(0)

// 测试1: 基本初始化和评估
bool TestBasicInitialization() {
    std::wcout << L"测试1: 基本初始化和评估..." << std::endl;
    
    const char* softwareId = "TestApp-Basic-2025";
    TrialMode mode = TrialMode::Days;
    uint32_t limit = 14;
    
    // 测试初始化
    int result = TrialInitialize(softwareId, mode, limit);
    TEST_ASSERT(result == HAL_OK, L"初始化应该成功");
    
    // 测试评估
    uint32_t remaining = 0, total = 0;
    result = TrialEvaluate(&remaining, &total);
    TEST_ASSERT(result == HAL_OK, L"评估应该成功");
    TEST_ASSERT(total == limit, L"总限制应该等于设置的限制");
    TEST_ASSERT(remaining <= total, L"剩余应该小于等于总数");
    
    std::wcout << L"  剩余天数: " << remaining << L" / " << total << std::endl;
    std::wcout << L"测试1: 通过" << std::endl;
    return true;
}

// 测试2: 按次数模式
bool TestRunCountMode() {
    std::wcout << L"测试2: 按次数模式..." << std::endl;
    
    const char* softwareId = "TestApp-Runs-2025";
    TrialMode mode = TrialMode::Runs;
    uint32_t limit = 5;
    
    // 初始化
    int result = TrialInitialize(softwareId, mode, limit);
    TEST_ASSERT(result == HAL_OK, L"初始化应该成功");
    
    // 测试消耗次数
    for (uint32_t i = 0; i < limit; ++i) {
        uint32_t remaining = 0, total = 0;
        result = TrialConsumeOneRun(&remaining, &total);
        TEST_ASSERT(result == HAL_OK, L"消耗次数应该成功");
        TEST_ASSERT(remaining == (limit - i - 1), L"剩余次数应该正确递减");
        
        std::wcout << L"  第 " << (i + 1) << L" 次使用，剩余: " << remaining << std::endl;
    }
    
    // 测试超出限制
    uint32_t remaining = 0, total = 0;
    result = TrialConsumeOneRun(&remaining, &total);
    TEST_ASSERT(result == HAL_E_TRIAL_EXPIRED, L"超出限制应该返回过期错误");
    
    std::wcout << L"测试2: 通过" << std::endl;
    return true;
}

// 测试3: 参数验证
bool TestParameterValidation() {
    std::wcout << L"测试3: 参数验证..." << std::endl;
    
    // 测试空软件ID
    TEST_EXPECT_ERROR(TrialInitialize(nullptr, TrialMode::Days, 14), 
                     HAL_ERR_INVALID_ARG, L"空软件ID应该返回无效参数错误");
    
    // 测试空字符串软件ID
    TEST_EXPECT_ERROR(TrialInitialize("", TrialMode::Days, 14), 
                     HAL_ERR_INVALID_ARG, L"空字符串软件ID应该返回无效参数错误");
    
    // 测试零限制（应该被自动调整为1）
    int result = TrialInitialize("TestApp-Zero-2025", TrialMode::Days, 0);
    TEST_ASSERT(result == HAL_OK, L"零限制应该被接受并调整为1");
    
    std::wcout << L"测试3: 通过" << std::endl;
    return true;
}

// 测试4: 默认配置
bool TestDefaultConfiguration() {
    std::wcout << L"测试4: 默认配置..." << std::endl;
    
    struct TestCase {
        const char* softwareId;
        TrialMode expectedMode;
        uint32_t expectedLimit;
        const wchar_t* description;
    };
    
    TestCase testCases[] = {
        {"VE-VideoEditor-2025", TrialMode::Runs, 30, L"VE前缀应该是30次"},
        {"BDL-BatchDownloader-2025", TrialMode::Days, 7, L"BDL前缀应该是7天"},
        {"CustomApp-2025", TrialMode::Days, 14, L"其他前缀应该是14天"}
    };
    
    for (const auto& testCase : testCases) {
        TrialMode mode;
        uint32_t limit;
        
        int result = TrialGetDefaultConfig(testCase.softwareId, mode, limit);
        TEST_ASSERT(result == HAL_OK, L"获取默认配置应该成功");
        TEST_ASSERT(mode == testCase.expectedMode, testCase.description);
        TEST_ASSERT(limit == testCase.expectedLimit, testCase.description);
        
        std::wcout << L"  " << testCase.softwareId << L": " 
                   << (mode == TrialMode::Days ? L"天数" : L"次数") << L" " << limit << std::endl;
    }
    
    std::wcout << L"测试4: 通过" << std::endl;
    return true;
}

// 测试5: 错误状态处理
bool TestErrorStates() {
    std::wcout << L"测试5: 错误状态处理..." << std::endl;
    
    // 测试未初始化状态
    uint32_t remaining = 0, total = 0;
    TEST_EXPECT_ERROR(TrialEvaluate(&remaining, &total), 
                     HAL_ERR_INVALID_ARG, L"未初始化时评估应该返回无效参数错误");
    
    TEST_EXPECT_ERROR(TrialConsumeOneRun(&remaining, &total), 
                     HAL_ERR_INVALID_ARG, L"未初始化时消耗应该返回无效参数错误");
    
    std::wcout << L"测试5: 通过" << std::endl;
    return true;
}

// 测试6: C++包装类
bool TestCppWrapper() {
    std::wcout << L"测试6: C++包装类..." << std::endl;
    
    try {
        trial::Manager manager;
        
        // 测试初始化
        bool success = manager.initialize("TestApp-Cpp-2025", TrialMode::Days, 7);
        TEST_ASSERT(success, L"C++包装类初始化应该成功");
        
        // 测试评估
        uint32_t remaining = 0, total = 0;
        success = manager.evaluate(&remaining, &total);
        TEST_ASSERT(success, L"C++包装类评估应该成功");
        TEST_ASSERT(total == 7, L"总限制应该是7");
        
        // 测试获取状态
        TrialState state;
        success = manager.getState(state);
        TEST_ASSERT(success, L"获取状态应该成功");
        TEST_ASSERT(state.totalLimit == 7, L"状态中的总限制应该是7");
        TEST_ASSERT(state.mode == TrialMode::Days, L"状态中的模式应该是按天");
        
        std::wcout << L"  C++包装类状态: " << state.remaining << L" / " << state.totalLimit << std::endl;
        
    } catch (const std::exception& e) {
        std::wcout << L"C++包装类测试异常: " << e.what() << std::endl;
        return false;
    }
    
    std::wcout << L"测试6: 通过" << std::endl;
    return true;
}

// 运行所有测试
bool RunAllTests() {
    std::wcout << L"开始运行试用期管理模块测试..." << std::endl;
    std::wcout << L"==============================" << std::endl;
    
    bool allPassed = true;
    
    // 运行各个测试
    if (!TestBasicInitialization()) allPassed = false;
    if (!TestRunCountMode()) allPassed = false;
    if (!TestParameterValidation()) allPassed = false;
    if (!TestDefaultConfiguration()) allPassed = false;
    if (!TestErrorStates()) allPassed = false;
    if (!TestCppWrapper()) allPassed = false;
    
    std::wcout << L"==============================" << std::endl;
    if (allPassed) {
        std::wcout << L"所有测试通过！✓" << std::endl;
    } else {
        std::wcout << L"部分测试失败！✗" << std::endl;
    }
    
    return allPassed;
}

// 主函数（仅在独立编译时使用）
#ifdef TRIAL_TEST_STANDALONE
int main() {
    // 设置控制台输出
    std::wcout.imbue(std::locale(""));
    
    bool success = RunAllTests();
    return success ? 0 : 1;
}
#endif

// 导出测试函数供其他模块调用
extern "C" {
    HALVAULT_API bool TrialRunTests() {
        return RunAllTests();
    }
}
