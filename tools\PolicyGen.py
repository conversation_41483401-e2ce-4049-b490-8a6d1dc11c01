#!/usr/bin/env python3
"""
PolicyGen.py  –  读取 manifest.json 生成 policy.h
用法：
  python PolicyGen.py manifest.json policy.h
说明：
  • manifest.json 格式: [ { "id": "VE00013", "mode": "Runs", "limit": 30 }, ... ]
  • 输出 policy.h, 包含:
      - constexpr uint32_t POLICY_VERSION
      - kPolicyKey[32], kPolicyHmac[32], kPolicyBlob[], kPolicyBlobSize
  • 加密方式: 仅示范用 XOR(key) 以免引入额外依赖。生产版请替换为 AES-CTR。
"""
import json, sys, hashlib, secrets, textwrap, pathlib, argparse
from Crypto.Cipher import AES  # requires pycryptodome
from Crypto.Util.Padding import pad

MODE_MAP = { 'Days': 0, 'Runs': 1 }

def encode_entry(sid: str, mode: str, limit: int) -> bytes:
    sid_hash = hashlib.sha256(sid.lower().encode('utf-8')).digest()  # 32B
    mode_byte = MODE_MAP[mode].to_bytes(1, 'little')
    limit_bytes = limit.to_bytes(2, 'little')  # 0..65535
    return sid_hash + mode_byte + limit_bytes

# remove xor_encrypt, add aes_ctr_encrypt
def aes_ctr_encrypt(data: bytes, key: bytes, nonce: bytes) -> bytes:
    cipher = AES.new(key, AES.MODE_CTR, nonce=nonce)
    return cipher.encrypt(data)

def parse_args():
    ap = argparse.ArgumentParser(description='Generate encrypted policy.h from manifest.')
    ap.add_argument('manifest', help='Input manifest.json')
    ap.add_argument('out', help='Output policy.h')
    ap.add_argument('--id', dest='sid', help='Filter by specific softwareId')
    return ap.parse_args()

def main():
    args = parse_args()
    manifest_path = pathlib.Path(args.manifest)
    out_path = pathlib.Path(args.out)

    manifest_all = json.loads(manifest_path.read_text(encoding='utf-8'))
    if args.sid:
        manifest = [e for e in manifest_all if e['id'] == args.sid]
        if not manifest:
            print(f"softwareId {args.sid} not found in manifest", file=sys.stderr)
            sys.exit(2)
    else:
        manifest = manifest_all

    entries = [encode_entry(e['id'], e['mode'], e['limit']) for e in manifest]
    if not entries:
        print("No entries to encode", file=sys.stderr)
        sys.exit(3)

    blob_plain = b''.join(entries)
    key = secrets.token_bytes(32)
    iv = secrets.token_bytes(16)  # 128-bit IV for AES-CBC
    
    # Use AES-CBC to match trialmanager.cpp
    cipher = AES.new(key, AES.MODE_CBC, iv)
    # Use PKCS#7 padding to match StreamTransformationFilter
    blob_padded = pad(blob_plain, AES.block_size)
    blob_enc = cipher.encrypt(blob_padded)
    
    version = secrets.randbelow(1<<30)
    
    # Use HMAC-SHA256 to match trialmanager.cpp: HMAC(key, blob + version)
    import hmac as hmac_lib
    h = hmac_lib.new(key, digestmod=hashlib.sha256)
    h.update(blob_enc)
    h.update(version.to_bytes(4, 'little'))
    hmac = h.digest()

    def to_c_array(data):
        return ','.join(f'0x{b:02X}' for b in data)

    header = textwrap.dedent(f"""
        // Auto-generated by PolicyGen.py – DO NOT EDIT.
        #pragma once
        #include <cstddef>
        constexpr uint32_t POLICY_VERSION = {version}u;
        constexpr unsigned char kPolicyKey[32] = {{ {to_c_array(key)} }};
        constexpr unsigned char kPolicyIV[16] = {{ {to_c_array(iv)} }};
        constexpr unsigned char kPolicyHmac[32] = {{ {to_c_array(hmac)} }};
        constexpr unsigned char kPolicyBlob[] = {{ {to_c_array(blob_enc)} }};
        constexpr size_t kPolicyBlobSize = sizeof(kPolicyBlob);
    """)
    out_path.write_text(header, encoding='utf-8')
    print(f"Generated {out_path} with {len(entries)} entries → blob {len(blob_enc)} bytes")

if __name__ == '__main__':
    main()
