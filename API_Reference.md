# HALVault API 参考文档

本文档详细描述了 HALVault 库的所有公开 API 接口。

## 📋 目录

- [核心 API](#核心-api)
- [试用管理 API](#试用管理-api)
- [硬件指纹 API](#硬件指纹-api)
- [授权管理 API](#授权管理-api)
- [在线激活 API](#在线激活-api)
- [序列号验证 API](#序列号验证-api)
- [安全防护 API](#安全防护-api)
- [错误处理 API](#错误处理-api)

## 🔧 核心 API

### HALVault_Initialize

初始化 HALVault 系统。

```cpp
HALVAULT_API int HALVault_Initialize(
    const wchar_t* softID,    // 软件标识符
    const wchar_t* serverUrl  // 服务器地址
) noexcept;
```

**参数**:
- `softID`: 软件唯一标识符，用于区分不同产品
- `serverUrl`: 授权服务器地址，用于在线激活

**返回值**:
- `HAL_OK`: 初始化成功
- 其他值: 初始化失败，通过 `HalGetLastError()` 获取详细错误

**示例**:
```cpp
int result = HALVault_Initialize(L"MyApp-v1.0", L"https://license.mycompany.com");
if (result != HAL_OK) {
    wprintf(L"初始化失败: %s\n", HalGetErrorText(HalGetLastError()));
}
```

### HALVault_GetFileMD5

计算文件的 MD5 哈希值。

```cpp
HALVAULT_API int HALVault_GetFileMD5(
    const wchar_t* pathOrNull,  // 文件路径，NULL 表示当前 DLL
    wchar_t* outMd5,           // 输出缓冲区
    size_t outMd5Chars         // 缓冲区大小（字符数）
) noexcept;
```

**参数**:
- `pathOrNull`: 文件路径，传入 `NULL` 时计算当前 DLL 的 MD5
- `outMd5`: 输出缓冲区，接收 32 位十六进制 MD5 字符串
- `outMd5Chars`: 缓冲区大小，至少需要 33 个字符（包含终止符）

**返回值**:
- `HAL_OK`: 计算成功
- `HAL_ERR_FILE_NOT_FOUND`: 文件不存在
- `HAL_ERR_INVALID_ARG`: 参数无效

### HALVault_VerificationFile

验证文件 MD5 值。

```cpp
HALVAULT_API int HALVault_VerificationFile(
    const wchar_t* inMd5  // 期望的 MD5 值
) noexcept;
```

## 🕒 试用管理 API

### TrialInitialize

初始化试用管理系统。

```cpp
HALVAULT_API int TrialInitialize(
    const char* softwareId  // 软件标识符
) noexcept;
```

**参数**:
- `softwareId`: 软件标识符，用于生成存储路径

**返回值**:
- `HAL_OK`: 初始化成功
- `HAL_ERR_INVALID_ARG`: 参数无效
- `HAL_ERR_DISK_IO`: 存储访问失败

### TrialEvaluate

评估当前试用状态（不消耗次数）。

```cpp
HALVAULT_API int TrialEvaluate(
    TrialStatus& out  // 输出试用状态
) noexcept;
```

**参数**:
- `out`: 输出的试用状态结构

**返回值**:
- `HAL_OK`: 评估成功，可以继续使用
- `HAL_E_TRIAL_EXPIRED`: 试用已过期
- `HAL_E_TRIAL_FROZEN`: 试用被冻结（检测到时间回拨）

**TrialStatus 结构**:
```cpp
struct TrialStatus {
    bool       allowed;        // 是否允许继续试用
    uint32_t   remaining;      // 剩余天数/次数
    uint32_t   runsUsed;       // 已使用次数
    uint32_t   limit;          // 限制值（天数或次数）
    TrialMode  mode;           // Days 或 Runs
    uint32_t   nowUnix;        // 当前 Unix 时间戳
    uint32_t   installUnix;    // 首次安装时间戳
};
```

### TrialConsumeOne

消耗一次使用次数（仅在按次数模式下有效）。

```cpp
HALVAULT_API int TrialConsumeOne() noexcept;
```

**返回值**:
- `HAL_OK`: 消耗成功
- `HAL_E_TRIAL_EXPIRED`: 试用已过期
- `HAL_E_TRIAL_FROZEN`: 试用被冻结

### TrialGetDefaultConfig

获取软件的默认试用配置。

```cpp
HALVAULT_API int TrialGetDefaultConfig(
    const char* softwareId,  // 软件标识符
    TrialMode& modeOut,      // 输出试用模式
    uint32_t& limitOut       // 输出限制值
) noexcept;
```

**参数**:
- `softwareId`: 软件标识符
- `modeOut`: 输出的试用模式（Days 或 Runs）
- `limitOut`: 输出的限制值（天数或次数）

## 🔍 硬件指纹 API

### hwid::GetHWID

获取硬件指纹（16 字节二进制）。

```cpp
HALVAULT_API std::array<uint8_t, 16> GetHWID(
    DiskFallback fallback = DiskFallback::None
);
```

### hwid::GetHWIDHex

获取硬件指纹的十六进制字符串表示。

```cpp
HALVAULT_API std::string GetHWIDHex(
    DiskFallback fallback = DiskFallback::None
);
```

**参数**:
- `fallback`: 磁盘序列号获取失败时的备用策略

**DiskFallback 枚举**:
```cpp
enum class DiskFallback {
    None,         // 不使用备用方案
    VolumeSerial  // 使用卷序列号作为备用
};
```

### hwid::GetDiskSerialByIndex

通过磁盘索引获取硬盘序列号。

```cpp
HALVAULT_API int GetDiskSerialByIndex(
    int index,                    // 磁盘索引
    std::string& outSerial,       // 输出序列号
    DiskFallback fallback = DiskFallback::None
);
```

### hwid::GetDiskSerialByLetter

通过驱动器字母获取硬盘序列号。

```cpp
HALVAULT_API int GetDiskSerialByLetter(
    wchar_t driveLetter,          // 驱动器字母 (C, D, ...)
    std::string& outSerial,       // 输出序列号
    DiskFallback fallback = DiskFallback::None
);
```

## 📄 授权管理 API

### SaveLicenseEncrypted

保存加密的授权文件。

```cpp
HALVAULT_API int SaveLicenseEncrypted(
    const std::string& filePath,   // 文件路径
    const std::string& contact,    // 联系方式
    const std::string& serial,     // 序列号
    const std::string& licenseB64, // Base64 编码的授权数据
    int quantity = 1               // 授权数量
);
```

### LoadLicenseEncrypted

加载并解密授权文件。

```cpp
HALVAULT_API int LoadLicenseEncrypted(
    const std::string& filePath,  // 文件路径
    LicenseInfo& out              // 输出授权信息
);
```

**LicenseInfo 结构**:
```cpp
struct LicenseInfo {
    std::array<std::uint8_t, 16> hwid;      // 硬件指纹
    std::string            contact;          // 联系方式
    std::uint8_t           softwareId;       // 软件 ID
    std::string            serial;           // 序列号
    std::uint32_t          expireUnix;       // 过期时间（Unix 时间戳）
    std::uint32_t          issuedAt;         // 签发时间
    std::uint8_t           payloadVer;       // 载荷版本
    std::uint16_t          quantity;         // 授权数量
    std::array<char, 8>    serialPrefix;     // 序列号前缀
    std::string            licenseStr;       // 原始授权字符串
};
```

## 🌐 在线激活 API

### RequestLicense

请求在线激活授权。

```cpp
HALVAULT_API int RequestLicense(
    const std::wstring& serverUrl,    // 服务器地址
    const std::string& apiKey,        // API 密钥
    const std::string& serial,        // 序列号
    const std::string& hwidHex,       // 硬件指纹（十六进制）
    const std::string& contact,       // 联系方式
    const int& softwareId,            // 软件 ID
    std::string& data,                // 返回的授权数据
    int& quantity,                    // 返回的授权数量
    int& httpCode,                    // HTTP 状态码
    bool ignoreCert = false           // 是否忽略 SSL 证书验证
);
```

### CheckLicense

检查在线授权状态。

```cpp
HALVAULT_API int CheckLicense(
    const std::wstring& serverUrl,    // 服务器地址
    const std::string& apiKey,        // API 密钥
    const std::string& serial,        // 序列号
    const std::string& hwidHex,       // 硬件指纹
    const int& softwareId,            // 软件 ID
    std::string& data,                // 返回数据
    int& httpCode,                    // HTTP 状态码
    bool ignoreCert = false           // 是否忽略证书验证
);
```

### RemoveLicense

移除在线授权。

```cpp
HALVAULT_API int RemoveLicense(
    const std::wstring& serverUrl,    // 服务器地址
    const std::string& apiKey,        // API 密钥
    const std::string& serial,        // 序列号
    const std::string& hwidHex,       // 硬件指纹
    std::string& data,                // 返回数据
    int& httpCode,                    // HTTP 状态码
    bool ignoreCert = false           // 是否忽略证书验证
);
```

## 🔐 序列号验证 API

### VerifySerial

验证序列号格式和内容。

```cpp
HALVAULT_API int VerifySerial(
    const std::string& serial,   // 序列号
    const std::string& contact,  // 联系方式
    SerialInfo& info            // 输出序列号信息
);
```

**SerialInfo 结构**:
```cpp
struct SerialInfo {
    std::uint8_t version;                      // 版本号
    std::uint8_t softwareId;                   // 软件 ID
    std::uint32_t expireUnix;                  // 过期时间
    std::array<std::uint8_t, 3> contactHash;  // 联系方式哈希
};
```

### ExtractSerialPrefix

提取序列号前缀。

```cpp
HALVAULT_API std::array<uint8_t, 8> ExtractSerialPrefix(
    const std::string& serial  // 序列号
);
```

## 🛡️ 安全防护 API

### anti::Start

启动后台安全防护。

```cpp
HALVAULT_API void Start();
```

### anti::Stop

停止安全防护。

```cpp
HALVAULT_API void Stop();
```

### anti::IsDebuggerPresentFast

快速检测调试器。

```cpp
HALVAULT_API bool IsDebuggerPresentFast();
```

### anti::IsRunningInVM

检测是否运行在虚拟机中。

```cpp
HALVAULT_API bool IsRunningInVM();
```

### anti::SetVmAllowed

设置是否允许在虚拟机中运行。

```cpp
HALVAULT_API void SetVmAllowed(bool allowed);
```

### anti::VerifySelfIntegrity

验证代码完整性。

```cpp
HALVAULT_API bool VerifySelfIntegrity();
```

## ❌ 错误处理 API

### HalGetLastError

获取最后一次错误码。

```cpp
HALVAULT_API int HalGetLastError();
```

### HalGetErrorText

根据错误码获取错误描述。

```cpp
HALVAULT_API const wchar_t* HalGetErrorText(int code);
```

### 错误码定义

```cpp
enum HalErr : int {
    HAL_OK = 0,                    // 操作成功
    HAL_ERR_FILE_NOT_FOUND,        // 文件未找到
    HAL_ERR_FORMAT,                // 文件格式错误
    HAL_ERR_CRC,                   // 校验失败
    HAL_ERR_CRYPTO,                // 加解密失败
    HAL_ERR_HW_MISMATCH,           // 硬件信息不匹配
    HAL_ERR_HTTP,                  // HTTP 请求失败
    HAL_ERR_JSON,                  // JSON 解析失败
    HAL_ERR_VERIFY_FAIL,           // 验证失败
    HAL_ERR_VERIFY_CONTACT,        // 需要联系支持
    HAL_ERR_DISK_IO,               // 磁盘 IO 错误
    HAL_ERR_INVALID_ARG,           // 非法参数
    HAL_ERR_LICENSE_EXPIRED,       // 授权过期
    HAL_ERR_CURLE_FAILED_INIT,     // CURL 初始化失败
    HAL_ERR_UNKNOWN = 0x7FFF       // 未知错误
};
```

---

**注意**: 所有 API 都是线程安全的，可以在多线程环境中安全调用。错误信息通过线程本地存储维护，不会相互干扰。
