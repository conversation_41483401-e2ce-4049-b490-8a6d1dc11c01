// TrialExample.cpp - 试用期管理模块使用示例
// 这个文件展示了如何使用TrialManager模块

#include "pch.h"
#include "TrialManager.hpp"
#include "halvault_error.h"
#include <iostream>
#include <iomanip>

// 示例：基本试用期管理
void BasicTrialExample() {
    std::wcout << L"=== 基本试用期管理示例 ===" << std::endl;
    
    // 1. 初始化试用期（14天）
    const char* softwareId = "MyApp-2025";
    TrialMode mode = TrialMode::Days;
    uint32_t limit = 14;
    
    int result = TrialInitialize(softwareId, mode, limit);
    if (result != HAL_OK) {
        std::wcout << L"初始化失败，错误码: 0x" << std::hex << result << std::endl;
        std::wcout << L"错误描述: " << HalGetErrorText(result) << std::endl;
        return;
    }
    
    std::wcout << L"试用期初始化成功！" << std::endl;
    
    // 2. 查询剩余时间
    uint32_t remaining = 0, total = 0;
    result = TrialEvaluate(&remaining, &total);
    if (result == HAL_OK) {
        std::wcout << L"剩余天数: " << remaining << L" / " << total << std::endl;
    } else {
        std::wcout << L"查询失败，错误码: 0x" << std::hex << result << std::endl;
        switch (result) {
        case HAL_E_TRIAL_EXPIRED:
            std::wcout << L"试用期已过期！" << std::endl;
            break;
        case HAL_E_TRIAL_FROZEN:
            std::wcout << L"试用期被冻结（检测到时钟回拨）！" << std::endl;
            break;
        case HAL_E_TRIAL_DEBUGGER:
            std::wcout << L"检测到调试器！" << std::endl;
            break;
        default:
            std::wcout << L"未知错误" << std::endl;
            break;
        }
    }
}

// 示例：按次数限制的试用期
void RunCountTrialExample() {
    std::wcout << L"\n=== 按次数限制试用期示例 ===" << std::endl;
    
    // 1. 初始化试用期（30次）
    const char* softwareId = "MyApp-Runs-2025";
    TrialMode mode = TrialMode::Runs;
    uint32_t limit = 30;
    
    int result = TrialInitialize(softwareId, mode, limit);
    if (result != HAL_OK) {
        std::wcout << L"初始化失败，错误码: 0x" << std::hex << result << std::endl;
        return;
    }
    
    std::wcout << L"按次数试用期初始化成功！" << std::endl;
    
    // 2. 模拟几次使用
    for (int i = 0; i < 5; ++i) {
        uint32_t remaining = 0, total = 0;
        result = TrialConsumeOneRun(&remaining, &total);
        
        if (result == HAL_OK) {
            std::wcout << L"第 " << (i + 1) << L" 次使用，剩余次数: " << remaining << L" / " << total << std::endl;
        } else {
            std::wcout << L"使用失败，错误码: 0x" << std::hex << result << std::endl;
            break;
        }
    }
}

// 示例：获取默认配置
void DefaultConfigExample() {
    std::wcout << L"\n=== 默认配置示例 ===" << std::endl;
    
    struct TestCase {
        const char* softwareId;
        const wchar_t* description;
    };
    
    TestCase testCases[] = {
        {"VE-VideoEditor-2025", L"视频编辑器"},
        {"BDL-BatchDownloader-2025", L"批量下载器"},
        {"MyCustomApp-2025", L"自定义应用"}
    };
    
    for (const auto& testCase : testCases) {
        TrialMode mode;
        uint32_t limit;
        
        int result = TrialGetDefaultConfig(testCase.softwareId, mode, limit);
        if (result == HAL_OK) {
            std::wcout << L"软件: " << testCase.description << std::endl;
            std::wcout << L"  ID: " << testCase.softwareId << std::endl;
            std::wcout << L"  模式: " << (mode == TrialMode::Days ? L"按天" : L"按次") << std::endl;
            std::wcout << L"  限制: " << limit << (mode == TrialMode::Days ? L" 天" : L" 次") << std::endl;
        }
    }
}

// 示例：错误处理
void ErrorHandlingExample() {
    std::wcout << L"\n=== 错误处理示例 ===" << std::endl;
    
    // 1. 测试无效参数
    int result = TrialInitialize(nullptr, TrialMode::Days, 14);
    if (result != HAL_OK) {
        std::wcout << L"预期的无效参数错误: 0x" << std::hex << result << std::endl;
    }
    
    // 2. 测试未初始化状态
    uint32_t remaining = 0, total = 0;
    result = TrialEvaluate(&remaining, &total);
    if (result != HAL_OK) {
        std::wcout << L"预期的未初始化错误: 0x" << std::hex << result << std::endl;
    }
    
    // 3. 显示所有错误码的描述
    std::wcout << L"\n错误码说明:" << std::endl;
    
    struct ErrorInfo {
        int code;
        const wchar_t* name;
    };
    
    ErrorInfo errors[] = {
        {HAL_OK, L"HAL_OK - 成功"},
        {HAL_ERR_INVALID_ARG, L"HAL_ERR_INVALID_ARG - 无效参数"},
        {HAL_E_TRIAL_EXPIRED, L"HAL_E_TRIAL_EXPIRED - 试用期已过期"},
        {HAL_E_TRIAL_FROZEN, L"HAL_E_TRIAL_FROZEN - 试用期被冻结"},
        {HAL_E_TRIAL_IO, L"HAL_E_TRIAL_IO - 数据读写失败"},
        {HAL_E_TRIAL_CRYPTO, L"HAL_E_TRIAL_CRYPTO - 加密/解密失败"},
        {HAL_E_TRIAL_DEBUGGER, L"HAL_E_TRIAL_DEBUGGER - 检测到调试器"},
        {HAL_E_TRIAL_HWID_MISMATCH, L"HAL_E_TRIAL_HWID_MISMATCH - 硬件指纹不匹配"}
    };
    
    for (const auto& error : errors) {
        std::wcout << L"  0x" << std::hex << std::setw(8) << std::setfill(L'0') << error.code 
                   << L" - " << error.name << std::endl;
    }
}

// C++包装类使用示例
void CppWrapperExample() {
    std::wcout << L"\n=== C++包装类示例 ===" << std::endl;
    
    try {
        trial::Manager manager;
        
        // 初始化
        if (manager.initialize("CppApp-2025", TrialMode::Days, 7)) {
            std::wcout << L"C++包装类初始化成功！" << std::endl;
            
            // 获取状态
            TrialState state;
            if (manager.getState(state)) {
                std::wcout << L"剩余: " << state.remaining << L" / " << state.totalLimit << std::endl;
                std::wcout << L"模式: " << (state.mode == TrialMode::Days ? L"按天" : L"按次") << std::endl;
            }
            
            // 评估
            uint32_t remaining, total;
            if (manager.evaluate(&remaining, &total)) {
                std::wcout << L"评估结果: " << remaining << L" / " << total << std::endl;
            }
            
        } else {
            std::wcout << L"C++包装类初始化失败，错误码: 0x" << std::hex << manager.getLastError() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::wcout << L"异常: " << e.what() << std::endl;
    }
}

// 主函数 - 运行所有示例
int main() {
    // 设置控制台输出为UTF-16
    std::wcout.imbue(std::locale(""));
    
    std::wcout << L"HALVault 试用期管理模块示例" << std::endl;
    std::wcout << L"================================" << std::endl;
    
    try {
        BasicTrialExample();
        RunCountTrialExample();
        DefaultConfigExample();
        ErrorHandlingExample();
        CppWrapperExample();
        
        std::wcout << L"\n所有示例运行完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::wcout << L"运行时异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::wcout << L"未知异常" << std::endl;
        return 1;
    }
    
    return 0;
}

// 注意：这个示例文件不会被编译到DLL中，它仅用于演示如何使用TrialManager模块
// 要使用这个示例，需要：
// 1. 创建一个控制台应用程序项目
// 2. 链接到HALVault.dll
// 3. 包含TrialManager.hpp头文件
// 4. 编译并运行
