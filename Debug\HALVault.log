﻿k:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Microsoft\VC\v160\Microsoft.CppBuild.targets(513,5): warning MSB8028: 中间目录(Debug\)包含从另一个项目(​HALVault.vcxproj)共享的文件。   这会导致错误的清除和重新生成行为。
  pch.cpp
  activate.cpp
  dllmain.cpp
  hwid.cpp
k:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include\atlcore.h(659,2): warning C4191: “reinterpret_cast”: 从“FARPROC”到“BOOL (__stdcall *)(DWORD)”的不安全转换
          通过结果指针调用该函数可能导致程序失败
  license.cpp
  SerialValidator.cpp
  正在生成代码...
    正在创建库 A:\KeyGenMaster\HALVault\Debug\HALVault.lib 和对象 A:\KeyGenMaster\HALVault\Debug\HALVault.exp
  HALVault.vcxproj -> A:\KeyGenMaster\HALVault\Debug\HALVault.dll
