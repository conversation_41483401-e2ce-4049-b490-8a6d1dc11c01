#include "pch.h"
#include "VaultManager.hpp"
#include "halvault_api.h"
#include "hwid.hpp"
#include "activate.hpp"
#include "license.hpp"
#include "SerialValidator.hpp"
#include "halvault_error.h"
#include "tools.h"
#include <ScopeGuard.h>
#include <array>
#include <windows.h>
#include <shlobj.h>   // SHGetKnownFolderPath
#include <chrono>
#include <filesystem>
#include <ctime>
#include <cstring>
#include <strsafe.h>

using namespace std::string_literals;
static const char apiKey [ ] = "x3K9bR7zN2vT1pF6";

// ---------------------- 静态成员 ---------------------------
std::wstring VaultManager::s_companyName = L"jiamisoft";
std::wstring VaultManager::s_projectName; // 默认空，动态初始化
std::wstring VaultManager::s_licensePath;
std::once_flag VaultManager::s_initFlag;
std::mutex     VaultManager::s_pathMutex;

// ----------------------- 辅助函数 --------------------------
static std::wstring GetExeBaseName()
{
    wchar_t path[MAX_PATH]{};
    DWORD len = GetModuleFileNameW(nullptr, path, MAX_PATH);
    if (len == 0 || len >= MAX_PATH) {
        return L"App";
        }

    std::wstring full(path, len);
    size_t pos = full.find_last_of(L"\\/");
    std::wstring fname = (pos == std::wstring::npos) ? full : full.substr(pos + 1);
    size_t dot = fname.find_last_of(L'.');
    if (dot != std::wstring::npos) {
        fname = fname.substr( 0 , dot );
        }
    return fname.empty() ? L"App" : fname;
}

static bool DirCreateRecursive(const std::wstring& dir)
{
    if (dir.empty( )) {
        return false;
        }

    if (CreateDirectoryW( dir.c_str( ) , nullptr )) {
        return true;
        }

    DWORD err = GetLastError();
    if (err == ERROR_ALREADY_EXISTS) {
        return true;
        }

    // 递归创建父目录
    size_t pos = dir.find_last_of(L"\\/");
    if (pos == std::wstring::npos) {
        return false;
        }

    std::wstring parent = dir.substr(0, pos);
    if (!DirCreateRecursive( parent )) {
        return false;
        }

    return CreateDirectoryW(dir.c_str(), nullptr) || GetLastError() == ERROR_ALREADY_EXISTS;
}

int  VaultManager::GetLastError( )
    { 
    return HalGetLastError( ); 
    }

const wchar_t* VaultManager::GetErrorText( int c )
    { 
    return HalGetErrorText( c ); 
    }

// ---------------------- 初始化函数 -------------------------
void VaultManager::Initialize(const std::wstring& projectName, const std::wstring& companyName)
{
    std::call_once(s_initFlag, [&]() {
        s_companyName = companyName.empty() ? L"jiamisoft" : companyName;
        s_projectName = projectName.empty() ? GetExeBaseName() : projectName;
    });
}

void VaultManager::SetLicenseFilePath(const std::wstring& fullPath)
{
    std::lock_guard<std::mutex> lk(s_pathMutex);
    s_licensePath = fullPath;
}

// ------------------- 路径解析 ------------------------------
std::wstring VaultManager::ResolveLicensePath()
{
    {
        std::lock_guard<std::mutex> lk(s_pathMutex);
        if (!s_licensePath.empty()) return s_licensePath;
    }

    // 确保已初始化
    Initialize();

    // 先尝试 %LOCALAPPDATA%\<Company>\<Project>
    PWSTR localAppPath = nullptr;
    if (SUCCEEDED(SHGetKnownFolderPath(FOLDERID_LocalAppData, 0, nullptr, &localAppPath))) {
        std::wstring path(localAppPath);
        CoTaskMemFree(localAppPath);
        path += L"\\" + s_companyName + L"\\" + s_projectName;
        DirCreateRecursive(path);
        s_licensePath = path + L"\\license.dat";
        return s_licensePath;
    }

    // 回退到 exe 目录
    wchar_t exePath[MAX_PATH]{};
    DWORD len = GetModuleFileNameW(nullptr, exePath, MAX_PATH);
    std::wstring dir;
    if (len != 0 && len < MAX_PATH) {
        dir.assign( exePath , len );
        size_t pos = dir.find_last_of( L"\\/" );
        if (pos != std::wstring::npos) {
            dir = dir.substr( 0 , pos );
            }
        }
    
    s_licensePath = dir + L"\\license.dat";
    return s_licensePath;
}

// ------------------- 功能接口实现 -------------------------
int VaultManager::GetHWID(wchar_t* hwidHexBuf, size_t bufLen)
{
    if (!hwidHexBuf || bufLen == 0) {
        HalSetLastError( HAL_ERR_INVALID_ARG );
        return HAL_ERR_INVALID_ARG;
        }

    VMP_GUARD("GetHWID");
    std::string hex = hwid::GetHWIDHex( );
    if (hex.empty()) {
        hwidHexBuf [ 0 ] = L'\0';
        HalSetLastError( HAL_ERR_INVALID_ARG );
        return HAL_ERR_INVALID_ARG;
        }

    // Validate HWID format: should be exactly 32 hex characters
    if (hex.size() != 32) {
        hwidHexBuf [ 0 ] = L'\0';
        HalSetLastError( HAL_ERR_INVALID_ARG );
        return HAL_ERR_INVALID_ARG;
        }

    // Check buffer size: need space for 32 wide chars + null terminator
    if (bufLen < hex.size( ) + 1) {
        HalSetLastError( HAL_ERR_INVALID_ARG );
        hwidHexBuf [ 0 ] = L'\0';
        return HAL_ERR_INVALID_ARG;
        }

    for (size_t i = 0; i < hex.size( ); ++i) {
        hwidHexBuf [ i ] = static_cast< wchar_t >( hex [ i ] );
        }

    hwidHexBuf[hex.size()] = L'\0';
    HalSetLastError(HAL_OK);
    return HAL_OK;
}

int VaultManager::CheckLicense(bool& hasFile, bool& valid)
{
    hasFile = false;
    valid   = false;

    std::wstring path = ResolveLicensePath();
    DWORD attr = GetFileAttributesW(path.c_str());

    if (attr == INVALID_FILE_ATTRIBUTES || ( attr & FILE_ATTRIBUTE_DIRECTORY )) {
        HalSetLastError( HAL_ERR_FILE_NOT_FOUND );
        return HAL_ERR_FILE_NOT_FOUND;
        }

    hasFile = true;

    LicenseInfo info{};
    std::string pathA = Tools::WStringToString( path, CP_ACP);

    VMP_BLOCK("CheckLicense"){
    int rc = LoadLicenseEncrypted(pathA, info);
    if (rc != HAL_OK) {
        return rc;
        }

    // 检查过期
    uint32_t now = static_cast<uint32_t>(std::time(nullptr));

    if (info.expireUnix != 0 && info.expireUnix < now) {
        HalSetLastError( HAL_ERR_VERIFY_FAIL );
        return HAL_ERR_VERIFY_FAIL;
        }

    valid = true;
    }
    HalSetLastError(HAL_OK);
    return HAL_OK;
}

/*
 * 函数: LicenseSecondsRemaining
 * 功能: 计算当前许可证距到期剩余的秒数
 * 参数:
 *   seconds [out] - 剩余秒数，如果返回错误时该值为 0
 * 返回值:
 *   HAL_OK             - 成功，返回 seconds
 *   HAL_ERR_VERIFY_FAIL- 许可证已过期或无效过期时间
 *   其他               - 从 LoadLicenseEncrypted 返回的错误码
 *
 * 实现步骤:
 *   1. 解析许可证文件路径，获取加密许可证数据
 *   2. 获取当前 Unix 时间，与许可证中的 expireUnix 比较
 *   3. 如已过期(或 expireUnix 为 0)则返回 HAL_ERR_VERIFY_FAIL
 *   4. 否则计算剩余秒数 = expireUnix - now 并返回
 */
int VaultManager::LicenseSecondsRemaining( uint32_t& seconds )
    {
    // 默认零秒，用于出现错误时确保一致
    seconds = 0;

    // 载入加密的许可证信息
    LicenseInfo info {};
    std::wstring path = ResolveLicensePath( );
    std::string pathA = Tools::WStringToString( path, CP_ACP );
    int rc = LoadLicenseEncrypted(pathA , info );
    if (rc != HAL_OK) return rc;

    // 计算当前时间
    uint32_t now = static_cast< uint32_t >( std::time( nullptr ) );

    // 如未设置过期时间(0)或已过期，则视为验证失败
    if (info.expireUnix == 0 || info.expireUnix <= now) {
        HalSetLastError( HAL_ERR_VERIFY_FAIL );
        return HAL_ERR_VERIFY_FAIL;
        }

    // 计算剩余秒数
    seconds = info.expireUnix - now;
    HalSetLastError( HAL_OK );
    return HAL_OK;
    }

int VaultManager::VerifySerial( const std::string& serial , 
    const std::string& contact , SerialInfo& info )
    {
    return ::VerifySerial( serial , contact , info );
    }

int VaultManager::RemoveOnline( const std::wstring& serverUrl ,
    const std::string& serial ,
    const std::string& hwidHex ,
    wchar_t* error ,
    size_t bufLen ,
    int& httpCode ,
    bool ignoreCert)
    {
    std::string data;
    int rc = RemoveLicense( serverUrl ,
        apiKey ,
        serial ,
        hwidHex ,
        data ,
        httpCode ,
        ignoreCert );

    if (data.length( )) {
        std::wstring msg = Tools::StringToWString( data );
        StringCchCopy( error , bufLen , msg.c_str( ) );
        }

    return rc;
    }

int VaultManager::CheckOnline( const std::wstring& serverUrl ,
    const std::string& serial ,
    const std::string& hwidHex ,
    const int& softwareId ,
    wchar_t* error ,
    size_t bufLen ,
    int& httpCode ,
    bool ignoreCert)
    {
    std::string data;
    int rc = ::CheckLicense(serverUrl,
        apiKey,
        serial,
        hwidHex,
        softwareId,
        data, 
        httpCode, 
        ignoreCert);
    std::wstring msg = Tools::StringToWString( data );
    StringCchCopy( error , bufLen , msg.c_str( ) );

    return rc;
    }
    
int VaultManager::ActivateOnline( const std::wstring& serverUrl ,
    const std::string& serial ,
    const std::string& contact ,
    const std::string& hwidHex , 
    const int& softwareId ,
    wchar_t* error ,
    size_t bufLen ,
    int  &httpCode,
    bool ignoreCert )
    {
    std::string data;
    int quantity;
    int rc = RequestLicense( serverUrl , 
        apiKey ,
        serial , 
        hwidHex , 
        contact , 
        softwareId,
        data , 
        quantity,
        httpCode,
        ignoreCert );

    if (rc != HAL_OK) {
        std::wstring msg = Tools::StringToWString( data, CP_UTF8 );
        StringCchCopy( error , bufLen , msg.c_str( ) );
        return rc;
        }

    std::wstring path = ResolveLicensePath( );
    // 确保目录存在
    size_t pos = path.find_last_of( L"\\/" );
    if (pos != std::wstring::npos) {
        DirCreateRecursive( path.substr( 0 , pos ) );
        }

    std::string pathA = Tools::WStringToString( path);
    rc = SaveLicenseEncrypted(pathA ,
        contact , 
        serial , 
        data ,
        quantity );
    return rc;
    }

int VaultManager::GetLicenseInfo( LicenseInfo& info )
    {
    std::wstring path = ResolveLicensePath( );
    std::string pathA = Tools::WStringToString( path);
    return LoadLicenseEncrypted(pathA , info );
    }

int VaultManager::GetLicenseInfoC( LicenseInfoC& out )
    {
    LicenseInfo tmp{};
    int rc = GetLicenseInfo( tmp );
    if (rc != HAL_OK) return rc;

    std::memcpy(out.hwid, tmp.hwid.data(), 16);
    strncpy_s(out.contact, sizeof(out.contact), tmp.contact.c_str(), _TRUNCATE);
    strncpy_s(out.serial,  sizeof(out.serial),  tmp.serial.c_str(),  _TRUNCATE);
    strncpy_s(out.serialPrefix, sizeof(out.serialPrefix), tmp.serialPrefix.data(), _TRUNCATE);
    strncpy_s(out.licenseB64, sizeof(out.licenseB64), tmp.licenseStr.c_str(), _TRUNCATE);

    out.softwareId = tmp.softwareId;
    out.expireUnix = tmp.expireUnix;
    out.issuedAt   = tmp.issuedAt;
    out.payloadVer = tmp.payloadVer;
    out.quantity   = tmp.quantity;
    return HAL_OK;
    }

// 7. 获取许可证过期时间详细信息和剩余时间

int VaultManager::GetLicenseExpiryDetails(uint32_t expireUnix, ExpiryDetails& details) {
    // 获取当前时间
    auto now = std::chrono::system_clock::now();
    auto now_t = std::chrono::system_clock::to_time_t(now);
    std::tm now_tm;
    localtime_s(&now_tm, &now_t); // 使用本地时间

    // 将过期时间转换为 tm 结构
    auto expiry_t = static_cast<time_t>(expireUnix);
    std::tm expiry_tm;
    localtime_s(&expiry_tm, &expiry_t);

    // 填充到期时间详细信息
    details.expiryYear = expiry_tm.tm_year + 1900;
    details.expiryMonth = expiry_tm.tm_mon + 1;
    details.expiryDay = expiry_tm.tm_mday;
    details.expiryHour = expiry_tm.tm_hour;
    details.expiryMinute = expiry_tm.tm_min;
    details.expirySecond = expiry_tm.tm_sec;

    // 计算剩余时间
    auto expiry_time_point = std::chrono::system_clock::from_time_t(expiry_t);
    auto duration = expiry_time_point - now;
    auto seconds = std::chrono::duration_cast<std::chrono::seconds>(duration).count();

    if (seconds <= 0) {
        // 许可已经过期，剩余时间设为 0
        details.years = 0;
        details.months = 0;
        details.days = 0;
        details.hours = 0;
        details.minutes = 0;
        details.seconds = 0;
        return 1; // 表示已过期
    }

    // 计算剩余年、月、日、小时、分钟、秒
    details.seconds = seconds % 60;
    auto minutes = seconds / 60;
    details.minutes = minutes % 60;
    auto hours = minutes / 60;
    details.hours = hours % 24;
    auto days = hours / 24;
    details.days = days % 365; // 简化处理，忽略闰年
    auto years = days / 365;
    details.years = static_cast<uint32_t>(years);
    details.months = static_cast<uint32_t>((days % 365) / 30); // 粗略处理，一个月按30天计算

    return 0; // 成功
}