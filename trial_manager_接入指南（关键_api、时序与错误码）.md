# TrialManager 接入指南（关键 API、时序与错误码）

> 适用文件：`TrialManager.secure.cpp`（Win7+，普通用户权限）。
>
> 特性回顾：AES-GCM（带 AAD）、六副本多数投票（Local/Roaming/ADS + HKCU 双值）、时钟回拨冻结、跨进程命名互斥、Append-only 证据日志（HMAC + 自动裁剪）。

---

## 目录

- [快速上手](#快速上手)
- [关键 API 一览](#关键-api-一览)
- [典型接入时序](#典型接入时序)
- [错误码与处理建议](#错误码与处理建议)
- [策略与配置](#策略与配置)
- [线程与并发](#线程与并发)
- [存储与权限](#存储与权限)
- [自检与故障排查](#自检与故障排查)

---

## 快速上手

**最小用法（按天 14 天）**

```cpp
TrialMode mode; uint32_t limit;
TrialGetDefaultConfig("VE-Any", mode, limit); // 或者你自己给定

// 初始化：建议在进程启动或模块加载点调用一次
int rc = TrialInitialize("VE-Any", mode, limit);
if (rc != HAL_OK) {
    // 可读 HalGetLastError() 或你项目内的等价函数
}

// 查询剩余额度（不消耗）
uint32_t remain=0, total=0;
rc = TrialEvaluate(&remain, &total);

// 在需要“按次消耗”的场景：
rc = TrialConsumeOneRun(&remain, &total);
```

**启用混合策略（先 5 次 → 再 7 天）**

```cpp
TrialInitialize("VE-Any", TrialMode::Days, 14); // 初始化即可
TrialConfigureMixed(5, 7); // 先用完 5 次，再进入 7 天
```

> 返回值：所有 API 都返回 `int`（`HAL_OK`/错误码），并通过 `HalSetLastError(...)`（或你项目中的替代实现）留下最近一次错误详情。

---

## 关键 API 一览

### `int TrialInitialize(const char* softwareId, TrialMode mode, uint32_t limit)`

**作用**：设置软件 ID、试用模式与额度，并确保首次落盘。

- **入参**：`softwareId`（必填）、`mode`（`Days` 或 `Runs`）、`limit`（0 会被归一为 1）
- **返回**：`HAL_OK` 或错误码（见下）
- **注意**：线程安全；内部会调用一次核心评估以创建/修复副本。

### `int TrialEvaluate(uint32_t* remaining, uint32_t* totalLimit)`

**作用**：查询当前剩余额度与总额度，不消耗次数。

- **返回**：`HAL_OK`、`HAL_E_TRIAL_EXPIRED`、`HAL_E_TRIAL_FROZEN` 等
- **注意**：`remaining/totalLimit` 允许为 `nullptr`。

### `int TrialConsumeOneRun(uint32_t* remaining, uint32_t* totalLimit)`

**作用**：在“按次阶段/模式”下消耗一次额度。

- **返回**：与 `TrialEvaluate` 相同；在“按天”阶段不会增加天数。

### `int TrialGetDefaultConfig(const char* softwareId, TrialMode& mode, uint32_t& limit)`

**作用**：按产品前缀返回默认策略（示例：`VE*` → 次数 30，`BDL*` → 7 天）。

- **注意**：你可以按产品线自定义；未匹配走 14 天按天模式。

### `int TrialConfigureMixed(uint32_t runsFirst, uint32_t daysAfter)`

**作用**：启用“先次数后天数”的混合策略。

- **注意**：需先调用 `TrialInitialize`；对已有文档就地更新并提升 `seqNo`。

---

## 典型接入时序

### A) 只按天（例如 14 天）

1. `TrialGetDefaultConfig`（或自定）
2. `TrialInitialize("SID", TrialMode::Days, 14)`
3. 每次启动/进入关键界面：`TrialEvaluate` → 到期则提示购买

### B) 只按次（例如 30 次）

1. `TrialInitialize("SID", TrialMode::Runs, 30)`
2. 进入“开始处理/导出”等关键动作前：`TrialConsumeOneRun`
3. UI 实时展示 `remaining/total`

### C) 混合（例如 先 5 次 → 再 7 天）

1. `TrialInitialize("SID", TrialMode::Days, 14)`（任意模式均可，实际由混合策略接管）
2. `TrialConfigureMixed(5, 7)`
3. 在“次数阶段”使用 `TrialConsumeOneRun`；次数耗尽后自动进入“按天阶段”，每天自然递减。

> 并发：内部有**进程内互斥**与**跨进程命名互斥**，避免副本竞争写；获取失败会降级继续，最坏情况多数投票仍能自愈。

---

## 错误码与处理建议

| 名称                    | 值（十六进制）      | 典型触发                               | 建议处理                          |
| --------------------- | ------------ | ---------------------------------- | ----------------------------- |
| `HAL_OK`              | `0x00000000` | 正常                                 | 正常流程                          |
| `HAL_E_INVALIDARG`    | `0x80070057` | 未 `TrialInitialize`、空 `softwareId` | 补充初始化；检查传参                    |
| `HAL_E_TRIAL_EXPIRED` | `0xA0010001` | 额度用尽（按天/按次）                        | 引导购买或激活；可给“申诉/联系支持”入口         |
| `HAL_E_TRIAL_FROZEN`  | `0xA0010002` | 发现明显回拨（>300s）或证据日志/副本回退            | 引导恢复系统时间并重试；必要时提示等待“时间恢复”自动解冻 |
| `HAL_E_IO`            | `0xA0010003` | 读写副本失败（文件/注册表任一都失败）                | 检查磁盘权限/空间/杀软；重试或降级提示          |
| `HAL_E_CRYPTO`        | `0xA0010004` | 解密失败（AAD/tag 校验不通过）                | 视为异常副本，系统会多数投票自愈；日志保留         |

> 注：API 返回错误码的同时会调用 `HalSetLastError(err)`。如果你的工程有 `HalGetLastError()` 或等价设施，可用于日志上报。

---

## 策略与配置

- **按天**：以“自然日”递增计数，跨天即累加；回拨不会减少。
- **按次**：`TrialConsumeOneRun` 时消耗；`TrialEvaluate` 不消耗。
- **混合**：`runsFirst` 用尽自动切换 `daysAfter`；无需改变调用习惯。
- **默认策略**：可在 `TrialGetDefaultConfig` 内按前缀定制；未覆盖时默认 14 天。

---

## 线程与并发

- **进程内**：`std::mutex` 保护全局状态。
- **跨进程**：命名互斥 `Local\\HALVault_{md5(sid)}`；等待 8s。
- **容错**：互斥获取失败时继续执行，最坏并发下依赖副本**多数投票**保证一致性。

---

## 存储与权限

- **副本**（共六份）：
  - `%LocalAppData%\\HALVault\\{md5}\\trial.bin`（+ ADS `:alt`）
  - `%AppData% (Roaming)\\HALVault\\{md5}\\trial.bin`（+ ADS `:alt`）
  - `HKCU\\Software\\HALVault\\{md5}` 下 `trial` 与 `alt` 两个 `REG_BINARY`
- **日志**：`%LocalAppData%\\HALVault\\{md5}\\trial.log`，记录 `seqNo/maxSeen/days`，HMAC-SHA256（截断 16B），超过 4096 条仅保留最近 1024 条。
- **权限**：全部为**普通用户**可写；兼容 **Windows 7**。

---

## 自检与故障排查

- **首次落盘失败**：检查 `Advapi32.lib` 链接、Crypto++ 头文件与库路径。
- **回拨冻结**：确认系统时间；恢复后再次 `TrialEvaluate` 会自动解冻（当 `now >= maxSeenUnix`）。
- **杀软/沙箱干扰**：文件写入失败/ADS 不可用时，多数投票仍能工作；注册表副本会兜底。
- **日志膨胀**：超过阈值自动裁剪；也可调整 `kLogMaxRecords/kLogTrimKeep` 编译期常量。

---

> 如需把本指南导出为发行包文档：可在仓库新增 `docs/TrialManager.md`，并将上面“快速上手/时序/错误码表”作为对外接口规范。

