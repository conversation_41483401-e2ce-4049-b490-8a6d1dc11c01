#!/usr/bin/env python3
"""
检查 HALVault.dll 的导出函数
"""
import subprocess
import sys
import os

def check_dll_exports(dll_path):
    """检查 DLL 的导出函数"""
    try:
        # 使用 dumpbin 检查导出
        result = subprocess.run(['dumpbin', '/exports', dll_path], 
                              capture_output=True, text=True, shell=True)
        
        if result.returncode != 0:
            print(f"❌ 无法检查 {dll_path}")
            print(f"错误: {result.stderr}")
            return False
            
        output = result.stdout
        
        # 查找试用相关的导出函数
        trial_functions = [
            'TrialInitialize',
            'TrialEvaluate', 
            'TrialConsumeOneRun',
            'TrialGetStatus',
            'TrialGetDefaultConfig'
        ]
        
        print(f"=== {dll_path} 导出函数检查 ===")
        
        found_functions = []
        for func in trial_functions:
            if func in output:
                found_functions.append(func)
                print(f"✅ {func}")
            else:
                print(f"❌ {func}")
                
        print(f"\n找到 {len(found_functions)}/{len(trial_functions)} 个函数")
        
        # 检查是否找到了 TrialGetStatus
        if 'TrialGetStatus' in found_functions:
            print("✅ TrialGetStatus 已正确导出")
            return True
        else:
            print("❌ TrialGetStatus 未找到!")
            print("\n完整导出列表:")
            print(output)
            return False
            
    except FileNotFoundError:
        print("❌ 找不到 dumpbin 工具")
        print("请确保已安装 Visual Studio 或 Windows SDK")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    # 查找可能的 DLL 位置
    possible_paths = [
        'HALVault.dll',
        'Debug/HALVault.dll', 
        'Release/HALVault.dll',
        'x64/Debug/HALVault.dll',
        'x64/Release/HALVault.dll',
        '../Debug/HALVault.dll',
        '../Release/HALVault.dll'
    ]
    
    dll_found = False
    for path in possible_paths:
        if os.path.exists(path):
            print(f"找到 DLL: {path}")
            check_dll_exports(path)
            dll_found = True
            break
    
    if not dll_found:
        print("❌ 找不到 HALVault.dll")
        print("请确保已编译 HALVault 项目")
        
        # 提供解决建议
        print("\n🔧 解决建议:")
        print("1. 确保在编译 HALVault.dll 时定义了 HALVAULT_EXPORTS")
        print("2. 检查项目设置 -> C/C++ -> 预处理器 -> 预处理器定义")
        print("3. 重新编译 HALVault 项目")

if __name__ == "__main__":
    main()
