﻿// dllmain.cpp : 定义 DLL 应用程序的入口点。
#include "pch.h"
static HMODULE g_hModule = nullptr;
BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        g_hModule = hModule;
        DisableThreadLibraryCalls(hModule); // 关键优化点
        break;
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        if (lpReserved == nullptr) {
        // 轻量、可重入、无加载行为的清理（若必须）
        }
        break;
    }
    return TRUE;
}

