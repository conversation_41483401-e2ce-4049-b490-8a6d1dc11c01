﻿#include "pch.h"
#include "halvault_api.h"
#include "halvault_error.h"
#include "AntiGuard.hpp"

static INIT_ONCE g_initOnce = INIT_ONCE_STATIC_INIT;
static DWORD g_initWinError = ERROR_SUCCESS;

static BOOL CALLBACK InitOnceCallback(PINIT_ONCE /*initOnce*/, PVOID /*parameter*/, PVOID* /*context*/)
{
    // 在此执行实际初始化工作；避免进行可能导致再次加载 DLL 的操作
    // 成功返回 TRUE；失败时设置 g_initWinError 并返回 FALSE（可重试）
    anti::Start();
    return TRUE;
}

static bool EnsureInitializedNoexcept() noexcept
{
    if (!InitOnceExecuteOnce(&g_initOnce, InitOnceCallback, nullptr, nullptr))
    {
        HalSetLastError(HAL_ERR_UNKNOWN);
        SetLastError(g_initWinError);
        return false;
    }
    return true;
}

extern "C" HALVAULT_API int HALVault_Initialize( const wchar_t* softID, const wchar_t* serverUrl ) noexcept
{
    if (softID) {
        anti::SetSoftwareId(softID);
        }
    
    if (serverUrl) {
        anti::SetServerUrl(serverUrl);
    }
    return EnsureInitializedNoexcept() ? 1 : 0;
}


