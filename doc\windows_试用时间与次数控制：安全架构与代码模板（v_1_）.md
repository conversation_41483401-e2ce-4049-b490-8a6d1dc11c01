# Windows 试用时间与次数控制：安全架构与代码模板（v1）

> 目标：在 Windows 平台实现**时间 + 次数**双重限制，具备**多副本存储、机器指纹绑定、加密与签名、防调试、时间回退防护**与**可选云端校验**。

---

## 1) 架构总览（组件与数据流）

```mermaid
flowchart LR
    A[应用主程序/GUI] -->|API调用| B[TrialGuard.dll]
    B --> C[存储层：注册表副本]
    B --> D[存储层：文件副本]
    B --> E[存储层：NTFS ADS校验流]
    B --> F[防护层：反调试/自校验]
    B --> G{可选：云端}
    G -->|/trial/sync| H[(服务端DB)]
    G -->|/trial/time| H
```

### 关键思想

- **多副本**：注册表 + 文件 + ADS（交叉校验，任何一份异常即触发风控策略）
- **密封存储**：AES-256-GCM 加密（机密性 + 完整性），绑定机器指纹与随机盐
- **反调试**：运行时检测调试与注入、代码段CRC自校验
- **时间防篡改**：本地“最近一次运行时间” + 可选服务端时间对比
- **云端协同（可选）**：服务端固化试用状态，防系统重装/镜像还原绕过

---

## 2) 数据模型（加密前的明文结构）

```json
{
  "schema": 1,
  "install_time": 1736428800,         // 首次安装时间（UTC秒）
  "expire_time": 1737696000,          // 试用到期时间（UTC秒）
  "last_run_time": 1736591600,        // 最近一次成功运行（UTC秒）
  "run_count": 12,                    // 已运行次数
  "max_runs": 30,                     // 最大允许次数
  "tamper_count": 0,                  // 可疑行为计数（时间回退/数据不一致等）
  "machine_hash": "base64-32B",      // 机器指纹哈希
  "salt": "base64-16B",              // 随机盐
  "app_id": "APP-XYZ-2025",         // 应用ID
  "version": "1.0.0"
}
```

> 序列化后以 **AES-256-GCM** 加密，随机 12B nonce，16B tag。密钥由（编译期内置的`APP_SECRET` + `machine_hash` + `salt`）通过 HKDF-SHA256 派生。

---

## 3) 公共 API 设计（C++）

```cpp
// TrialGuard.h
namespace trial {
    enum class Status {
        OK, Expired, ExceedRuns, Debugger, Tampered, StorageError, NetworkRequired
    };

    struct State {
        Status status;
        int    run_count;
        int    max_runs;
        uint64_t now_utc;
        uint64_t expire_utc;
        bool   online_hint;
    };

    // 初始化：加载/解密/校验，必要时创建全新试用状态
    bool Init(const wchar_t* appId, int trialDays, int maxRuns);

    // 每次启动时调用：原子性 + 自增一次并持久化（多副本）
    State CheckAndConsumeOneRun();

    // 手动拉起与服务端同步（可选）
    State SyncWithServer(const wchar_t* endpointUrl, const wchar_t* deviceIdOpt);
}
```

---

## 4) C++ 实现骨架（可直接集成到 DLL/静态库）

> 说明：代码采用 Win32 API + CNG(Bcrypt) + WinHTTP。为突出核心思路，以下给出**完整骨架**与**关键函数实现**。生产中请继续做**控制流混淆**与**字符串加密**。

```cpp
// TrialGuard.cpp (C++17)
#define NOMINMAX
#include <Windows.h>
#include <winternl.h>
#include <bcrypt.h>
#include <winhttp.h>
#include <shlwapi.h>
#include <string>
#include <vector>
#include <optional>
#include <cstdint>
#include <atomic>
#include <memory>

#pragma comment(lib, "bcrypt.lib")
#pragma comment(lib, "winhttp.lib")
#pragma comment(lib, "shlwapi.lib")

namespace trial {
// ========= 编译期常量（建议做字符串密文 + 运行时解密） =========
static const char* APP_SECRET = "\x2A\x91\x5F\xC8\x...32bytes_total..."; // 32字节随机
static const wchar_t* REG_PATH = L"Software\\Microsoft\\WAB\\{F1E4-7C9A-...}"; // 伪装路径
static const wchar_t* FILE_PATH = L"C:\\ProgramData\\Microsoft\\WAB\\wab.dat";
static const wchar_t* ADS_NAME  = L":meta"; // wab.dat:meta

// ========= 工具函数：时间 =========
static inline uint64_t UtcNow() {
    FILETIME ft; GetSystemTimeAsFileTime(&ft);
    ULARGE_INTEGER uli; uli.LowPart = ft.dwLowDateTime; uli.HighPart = ft.dwHighDateTime;
    return (uli.QuadPart - 116444736000000000ULL) / 10000000ULL; // to Unix seconds
}

// ========= 工具：简单反调试与自校验（示意） =========
static bool IsDebugging() {
    if (IsDebuggerPresent()) return true;
    BOOL remote = FALSE; CheckRemoteDebuggerPresent(GetCurrentProcess(), &remote);
    if (remote) return true;
    // 轻量时间扰动检测（防单步调试）
    auto t0 = __rdtsc(); Sleep(1); auto t1 = __rdtsc();
    if ((t1 - t0) > (10000000ull)) return true; // 粗略阈值
    return false;
}

// ========= 机器指纹（组合 + 哈希） =========
static std::optional<std::vector<uint8_t>> GetMachineHash(); // 见下方实现

// ========= 加解密：HKDF-SHA256 + AES-256-GCM =========
static bool HKDF_SHA256(const uint8_t* secret, size_t secretLen,
                        const uint8_t* salt, size_t saltLen,
                        const uint8_t* info, size_t infoLen,
                        uint8_t out32[32]);

static bool AES_GCM_Encrypt(const std::vector<uint8_t>& key,
                            const uint8_t* plaintext, size_t ptLen,
                            const uint8_t* aad, size_t aadLen,
                            std::vector<uint8_t>& out); // out = nonce(12)+ct+tag(16)

static bool AES_GCM_Decrypt(const std::vector<uint8_t>& key,
                            const uint8_t* in, size_t inLen,
                            const uint8_t* aad, size_t aadLen,
                            std::vector<uint8_t>& plaintext);

// ========= 存储：注册表 / 文件 / ADS =========
static bool LoadFromRegistry(std::vector<uint8_t>& blob);
static bool SaveToRegistry(const std::vector<uint8_t>& blob);
static bool LoadFromFile(std::vector<uint8_t>& blob);
static bool SaveToFile(const std::vector<uint8_t>& blob);
static bool LoadFromADS(std::vector<uint8_t>& blob);
static bool SaveToADS(const std::vector<uint8_t>& blob);

// ========= JSON（极简）序列化/反序列化（可换 RapidJSON） =========
struct Doc { // 与数据模型对应（省略JSON解析具体实现细节）
    uint64_t install_time{}, expire_time{}, last_run_time{};
    uint32_t run_count{}, max_runs{}; uint32_t tamper_count{};
    std::vector<uint8_t> machine_hash; std::vector<uint8_t> salt;
    std::wstring app_id; std::wstring version;
};
static bool EncodeDoc(const Doc& d, std::vector<uint8_t>& outJson);
static bool DecodeDoc(const uint8_t* p, size_t n, Doc& d);

// ========= 进程内状态 =========
static std::atomic<bool> g_inited{false};
static Doc g_doc; static std::vector<uint8_t> g_key;

// ========= 初始化 =========
bool Init(const wchar_t* appId, int trialDays, int maxRuns) {
    if (g_inited.load()) return true;
    if (IsDebugging()) return false;

    // 1) 读取三处密文，选出“多数一致”的一份；否则使用任意可解密的一份。
    std::vector<std::vector<uint8_t>> blobs; std::vector<uint8_t> b;
    if (LoadFromRegistry(b)) blobs.push_back(b);
    if (LoadFromFile(b))     blobs.push_back(b);
    if (LoadFromADS(b))      blobs.push_back(b);

    auto mh = GetMachineHash(); if (!mh) return false;

    // 2) 密钥派生：HKDF(APP_SECRET, salt||machine_hash, info=appId)
    auto deriveKey = [&](const std::vector<uint8_t>& salt){
        uint8_t out[32];
        std::vector<uint8_t> ikm(32 + mh->size());
        memcpy(ikm.data(), APP_SECRET, 32);
        memcpy(ikm.data()+32, mh->data(), mh->size());
        HKDF_SHA256(ikm.data(), ikm.size(), salt.data(), salt.size(),
                    (const uint8_t*)appId, wcslen(appId)*sizeof(wchar_t), out);
        return std::vector<uint8_t>(out, out+32);
    };

    Doc best; bool have=false;
    for (auto& enc : blobs) {
        // 尝试从密文中先解析salt（头部固定布局：salt16 + payload）
        if (enc.size() < 16+12+16) continue;
        std::vector<uint8_t> salt(enc.begin(), enc.begin()+16);
        auto key = deriveKey(salt);
        std::vector<uint8_t> pt;
        if (!AES_GCM_Decrypt(key, enc.data()+16, enc.size()-16, nullptr, 0, pt)) continue;
        Doc d; if (!DecodeDoc(pt.data(), pt.size(), d)) continue;
        if (d.machine_hash != *mh) continue; // 机器绑定
        best = d; g_key = key; have = true; break;
    }

    if (!have) {
        // 3) 首次安装，生成试用文档
        g_doc = {};
        g_doc.install_time = UtcNow();
        g_doc.expire_time  = g_doc.install_time + (uint64_t)trialDays*86400ull;
        g_doc.last_run_time= g_doc.install_time;
        g_doc.run_count = 0; g_doc.max_runs = (uint32_t)maxRuns; g_doc.tamper_count=0;
        g_doc.machine_hash = *mh; g_doc.salt.resize(16);
        ::BCryptGenRandom(NULL, g_doc.salt.data(), (ULONG)g_doc.salt.size(), BCRYPT_USE_SYSTEM_PREFERRED_RNG);
        g_doc.app_id = appId; g_doc.version = L"1.0.0";
        g_key = deriveKey(g_doc.salt);

        std::vector<uint8_t> json, ct;
        EncodeDoc(g_doc, json);
        AES_GCM_Encrypt(g_key, json.data(), json.size(), nullptr, 0, ct);

        // 布局：salt(16) + gcm_blob
        std::vector<uint8_t> blob; blob.reserve(16+ct.size());
        blob.insert(blob.end(), g_doc.salt.begin(), g_doc.salt.end());
        blob.insert(blob.end(), ct.begin(), ct.end());
        SaveToRegistry(blob); SaveToFile(blob); SaveToADS(blob);
    } else {
        g_doc = best;
    }

    g_inited = true; return true;
}

// ========= 消耗一次运行 =========
State CheckAndConsumeOneRun() {
    State st{}; st.now_utc = UtcNow(); st.expire_utc = g_doc.expire_time;
    if (IsDebugging()) { st.status = Status::Debugger; return st; }

    // 时间回退判定
    if (st.now_utc + 300 < g_doc.last_run_time) {
        if (++g_doc.tamper_count > 2) { st.status = Status::Tampered; return st; }
    }

    if (st.now_utc > g_doc.expire_time) { st.status = Status::Expired; return st; }
    if (g_doc.run_count >= g_doc.max_runs) { st.status = Status::ExceedRuns; return st; }

    g_doc.run_count++; g_doc.last_run_time = st.now_utc;

    // 持久化（多副本）
    std::vector<uint8_t> json, ct, blob;
    EncodeDoc(g_doc, json);
    AES_GCM_Encrypt(g_key, json.data(), json.size(), nullptr, 0, ct);
    blob.insert(blob.end(), g_doc.salt.begin(), g_doc.salt.end());
    blob.insert(blob.end(), ct.begin(), ct.end());
    SaveToRegistry(blob); SaveToFile(blob); SaveToADS(blob);

    st.status = Status::OK; st.run_count = g_doc.run_count; st.max_runs = g_doc.max_runs; st.online_hint=false;
    return st;
}

// ========= 与服务端同步（可选，示意） =========
State SyncWithServer(const wchar_t* endpointUrl, const wchar_t* deviceIdOpt) {
    State st = CheckAndConsumeOneRun();
    // TODO: 使用 WinHTTP POST 发送（machine_hash、install_time、run_count、expire_time、HMAC）
    // 服务器可返回 authoritative 状态与当前 UTC 时间。
    // 若发现本地回退或服务端已到期：标记 Tampered/Expired 并落盘。
    st.online_hint = true; return st;
}

// ======= 省略：HKDF、AES-GCM、存储实现（下面给出参考实现片段） =======
}
```

### 关键实现片段（可直接粘贴补齐）

**HKDF-SHA256（基于 CNG）**

```cpp
static bool HKDF_SHA256(const uint8_t* secret, size_t secretLen,
                        const uint8_t* salt, size_t saltLen,
                        const uint8_t* info, size_t infoLen,
                        uint8_t out32[32]) {
    BCRYPT_ALG_HANDLE hAlg = nullptr;
    if (BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_SHA256_ALGORITHM, nullptr, 0) < 0) return false;

    // PRK = HMAC(salt, secret)
    BCRYPT_HASH_HANDLE hHash = nullptr; DWORD objLen=0, cb=0; PBYTE obj=nullptr;
    BCryptGetProperty(hAlg, BCRYPT_OBJECT_LENGTH, (PBYTE)&objLen, sizeof(DWORD), &cb, 0);
    obj = (PBYTE)HeapAlloc(GetProcessHeap(), 0, objLen);
    if (BCryptCreateHash(hAlg, &hHash, obj, objLen, (PUCHAR)salt, (ULONG)saltLen, 0) < 0) goto L_ERR;
    BCryptHashData(hHash, (PUCHAR)secret, (ULONG)secretLen, 0);
    uint8_t prk[32]; BCryptFinishHash(hHash, prk, 32, 0); BCryptDestroyHash(hHash); hHash=nullptr;

    // T(1) = HMAC(PRK, T(0)||info||0x01)
    if (BCryptCreateHash(hAlg, &hHash, obj, objLen, prk, 32, 0) < 0) goto L_ERR;
    if (info && infoLen) BCryptHashData(hHash, (PUCHAR)info, (ULONG)infoLen, 0);
    const uint8_t ctr = 1; BCryptHashData(hHash, (PUCHAR)&ctr, 1, 0);
    BCryptFinishHash(hHash, out32, 32, 0);
    HeapFree(GetProcessHeap(), 0, obj); BCryptDestroyHash(hHash); BCryptCloseAlgorithmProvider(hAlg,0); return true;
L_ERR:
    if (hHash) BCryptDestroyHash(hHash); if (obj) HeapFree(GetProcessHeap(),0,obj); if (hAlg) BCryptCloseAlgorithmProvider(hAlg,0);
    return false;
}
```

**AES-256-GCM（CNG）**

```cpp
static bool AES_GCM_Encrypt(const std::vector<uint8_t>& key,
                            const uint8_t* pt, size_t ptLen,
                            const uint8_t* aad, size_t aadLen,
                            std::vector<uint8_t>& out) {
    BCRYPT_ALG_HANDLE hAlg=nullptr; BCRYPT_KEY_HANDLE hKey=nullptr; NTSTATUS s;
    if ((s=BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_AES_ALGORITHM, nullptr, 0))<0) return false;
    BCryptSetProperty(hAlg, BCRYPT_CHAINING_MODE, (PUCHAR)BCRYPT_CHAIN_MODE_GCM, sizeof(BCRYPT_CHAIN_MODE_GCM),0);
    DWORD objLen=0, cb=0; BCryptGetProperty(hAlg, BCRYPT_OBJECT_LENGTH, (PUCHAR)&objLen, sizeof(DWORD), &cb, 0);
    std::vector<uint8_t> keyObj(objLen);
    if ((s=BCryptGenerateSymmetricKey(hAlg,&hKey,keyObj.data(),objLen,(PUCHAR)key.data(),(ULONG)key.size(),0))<0) goto ERR;

    BCRYPT_AUTHENTICATED_CIPHER_MODE_INFO info; BCRYPT_INIT_AUTH_MODE_INFO(info);
    std::vector<uint8_t> nonce(12), tag(16); BCryptGenRandom(NULL, nonce.data(), (ULONG)nonce.size(), BCRYPT_USE_SYSTEM_PREFERRED_RNG);
    info.pbNonce = nonce.data(); info.cbNonce = (ULONG)nonce.size();
    info.pbTag   = tag.data(); info.cbTag   = (ULONG)tag.size();
    info.pbAuthData = const_cast<PUCHAR>(aad); info.cbAuthData = (ULONG)aadLen;

    DWORD ctLen=0; BCryptEncrypt(hKey, (PUCHAR)pt, (ULONG)ptLen, &info, nullptr, 0, nullptr, 0, &ctLen, 0);
    std::vector<uint8_t> ct(ctLen);
    if ((s=BCryptEncrypt(hKey, (PUCHAR)pt, (ULONG)ptLen, &info, nullptr, 0, ct.data(), ctLen, &ctLen, 0))<0) goto ERR;

    out.clear(); out.insert(out.end(), nonce.begin(), nonce.end());
    out.insert(out.end(), ct.begin(), ct.end());
    out.insert(out.end(), tag.begin(), tag.end());
    BCryptDestroyKey(hKey); BCryptCloseAlgorithmProvider(hAlg,0); return true;
ERR:
    if (hKey) BCryptDestroyKey(hKey); if (hAlg) BCryptCloseAlgorithmProvider(hAlg,0); return false;
}

static bool AES_GCM_Decrypt(const std::vector<uint8_t>& key,
                            const uint8_t* in, size_t inLen,
                            const uint8_t* aad, size_t aadLen,
                            std::vector<uint8_t>& pt) {
    if (inLen < 12+16) return false; // nonce + tag
    const uint8_t* nonce = in; size_t nlen=12;
    const uint8_t* tag   = in+inLen-16; size_t tlen=16;
    const uint8_t* ct    = in+nlen; size_t clen=inLen-nlen-tlen;

    BCRYPT_ALG_HANDLE hAlg=nullptr; BCRYPT_KEY_HANDLE hKey=nullptr; NTSTATUS s;
    if ((s=BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_AES_ALGORITHM, nullptr, 0))<0) return false;
    BCryptSetProperty(hAlg, BCRYPT_CHAINING_MODE, (PUCHAR)BCRYPT_CHAIN_MODE_GCM, sizeof(BCRYPT_CHAIN_MODE_GCM),0);
    DWORD objLen=0, cb=0; BCryptGetProperty(hAlg, BCRYPT_OBJECT_LENGTH, (PUCHAR)&objLen, sizeof(DWORD), &cb, 0);
    std::vector<uint8_t> keyObj(objLen);
    if ((s=BCryptGenerateSymmetricKey(hAlg,&hKey,keyObj.data(),objLen,(PUCHAR)key.data(),(ULONG)key.size(),0))<0) goto ERR;

    BCRYPT_AUTHENTICATED_CIPHER_MODE_INFO info; BCRYPT_INIT_AUTH_MODE_INFO(info);
    info.pbNonce = const_cast<PUCHAR>(nonce); info.cbNonce = (ULONG)nlen;
    info.pbTag   = const_cast<PUCHAR>(tag);   info.cbTag   = (ULONG)tlen;
    info.pbAuthData = const_cast<PUCHAR>(aad); info.cbAuthData = (ULONG)aadLen;

    DWORD ptLen=0; BCryptDecrypt(hKey, (PUCHAR)ct, (ULONG)clen, &info, nullptr, 0, nullptr, 0, &ptLen, 0);
    pt.resize(ptLen);
    if ((s=BCryptDecrypt(hKey, (PUCHAR)ct, (ULONG)clen, &info, nullptr, 0, pt.data(), ptLen, &ptLen, 0))<0) goto ERR;
    pt.resize(ptLen);
    BCryptDestroyKey(hKey); BCryptCloseAlgorithmProvider(hAlg,0); return true;
ERR:
    if (hKey) BCryptDestroyKey(hKey); if (hAlg) BCryptCloseAlgorithmProvider(hAlg,0); return false;
}
```

**注册表/文件/ADS 存取**（示意，需补足错误处理与隐藏策略）

```cpp
static bool LoadFromRegistry(std::vector<uint8_t>& blob){
    HKEY h; if (RegOpenKeyExW(HKEY_CURRENT_USER, REG_PATH, 0, KEY_READ, &h)) return false;
    DWORD sz=0; if (RegQueryValueExW(h, L"Cache", 0, nullptr, nullptr, &sz)) { RegCloseKey(h); return false; }
    blob.resize(sz); auto ok = RegQueryValueExW(h, L"Cache", 0, nullptr, blob.data(), &sz)==0; RegCloseKey(h); return ok;
}
static bool SaveToRegistry(const std::vector<uint8_t>& blob){
    HKEY h; if (RegCreateKeyExW(HKEY_CURRENT_USER, REG_PATH, 0, nullptr, 0, KEY_WRITE, nullptr, &h, nullptr)) return false;
    auto ok = RegSetValueExW(h, L"Cache", 0, REG_BINARY, blob.data(), (DWORD)blob.size())==0; RegCloseKey(h); return ok;
}

static bool LoadFromFile(std::vector<uint8_t>& blob){
    HANDLE f = CreateFileW(FILE_PATH, GENERIC_READ, FILE_SHARE_READ, nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_HIDDEN, nullptr);
    if (f==INVALID_HANDLE_VALUE) return false; DWORD sz=GetFileSize(f, nullptr); blob.resize(sz);
    DWORD rd=0; BOOL ok = ReadFile(f, blob.data(), sz, &rd, nullptr); CloseHandle(f); return ok && rd==sz;
}
static bool SaveToFile(const std::vector<uint8_t>& blob){
    SHCreateDirectoryExW(nullptr, L"C:\\ProgramData\\Microsoft\\WAB", nullptr);
    HANDLE f = CreateFileW(FILE_PATH, GENERIC_WRITE, 0, nullptr, CREATE_ALWAYS, FILE_ATTRIBUTE_HIDDEN, nullptr);
    if (f==INVALID_HANDLE_VALUE) return false; DWORD wr=0; BOOL ok = WriteFile(f, blob.data(), (DWORD)blob.size(), &wr, nullptr); CloseHandle(f); return ok && wr==blob.size();
}

static bool LoadFromADS(std::vector<uint8_t>& blob){
    std::wstring p = std::wstring(FILE_PATH) + ADS_NAME; HANDLE f = CreateFileW(p.c_str(), GENERIC_READ, FILE_SHARE_READ, nullptr, OPEN_EXISTING, 0, nullptr);
    if (f==INVALID_HANDLE_VALUE) return false; DWORD sz=GetFileSize(f, nullptr); blob.resize(sz);
    DWORD rd=0; BOOL ok = ReadFile(f, blob.data(), sz, &rd, nullptr); CloseHandle(f); return ok && rd==sz;
}
static bool SaveToADS(const std::vector<uint8_t>& blob){
    std::wstring p = std::wstring(FILE_PATH) + ADS_NAME; HANDLE f = CreateFileW(p.c_str(), GENERIC_WRITE, 0, nullptr, CREATE_ALWAYS, 0, nullptr);
    if (f==INVALID_HANDLE_VALUE) return false; DWORD wr=0; BOOL ok = WriteFile(f, blob.data(), (DWORD)blob.size(), &wr, nullptr); CloseHandle(f); return ok && wr==blob.size();
}
```

**机器指纹（示例：卷序列号 + 主板UUID + CPU信息）**

> 生产中可引入 WMI（较重）或使用固定几项的哈希；下面给出轻量实现思路（示意）。

```cpp
static void HashCat(std::vector<uint8_t>& sink, const void* p, size_t n){
    // 这里可替换为真正的 SHA-256；为简短起见，示意拼接后在 HKDF 中使用
    const uint8_t* b=(const uint8_t*)p; sink.insert(sink.end(), b, b+n);
}
static std::optional<std::vector<uint8_t>> GetMachineHash(){
    std::vector<uint8_t> acc;
    // C: 卷序列
    DWORD vsn=0; GetVolumeInformationW(L"C:\\", nullptr,0,&vsn,nullptr,nullptr,nullptr,0); HashCat(acc,&vsn,sizeof(vsn));
    // CPU 个数 & 特征（简化）
    SYSTEM_INFO si; GetSystemInfo(&si); HashCat(acc,&si.dwNumberOfProcessors,sizeof(si.dwNumberOfProcessors));
    // 计算机名
    wchar_t name[256]; DWORD n=256; GetComputerNameW(name,&n); HashCat(acc,name,n*sizeof(wchar_t));
    // 最终压缩到32字节（HKDF里会再哈希），这里直接截断/留给HKDF
    if (acc.empty()) return std::nullopt; acc.resize(32, 0);
    return acc;
}
```

**JSON 编解码**（建议换成 RapidJSON，下面留空壳）

```cpp
static bool EncodeDoc(const Doc& d, std::vector<uint8_t>& out){
    // 生产使用：RapidJSON / nlohmann_json
    char buf[512];
    // 简易拼接（演示用）
    int m = snprintf(buf, sizeof(buf),
        "{\"schema\":1,\"install_time\":%llu,\"expire_time\":%llu,\"last_run_time\":%llu,\"run_count\":%u,\"max_runs\":%u,\"tamper_count\":%u}",
        (unsigned long long)d.install_time,(unsigned long long)d.expire_time,(unsigned long long)d.last_run_time,
        d.run_count, d.max_runs, d.tamper_count);
    if (m<=0) return false; out.assign(buf, buf+m); return true;
}
static bool DecodeDoc(const uint8_t* p, size_t n, Doc& d){
    // 生产使用：JSON库解析；此处演示仅解析关键字段
    std::string s((const char*)p, n);
    auto get = [&](const char* key)->uint64_t{
        auto pos = s.find(key); if (pos==std::string::npos) return 0; pos=s.find(':',pos); if (pos==std::string::npos) return 0; return _strtoui64(s.c_str()+pos+1,nullptr,10);
    };
    d.install_time = get("install_time"); d.expire_time = get("expire_time"); d.last_run_time=get("last_run_time");
    d.run_count=(uint32_t)get("run_count"); d.max_runs=(uint32_t)get("max_runs"); d.tamper_count=(uint32_t)get("tamper_count");
    return d.expire_time!=0;
}
```

---

## 5) 主程序集成示例（MFC/Win32）

```cpp
#include "TrialGuard.h"

BOOL CYourApp::InitInstance() {
    trial::Init(L"APP-XYZ-2025", /*trialDays=*/15, /*maxRuns=*/30);
    auto st = trial::CheckAndConsumeOneRun();
    switch (st.status) {
    case trial::Status::OK: break;
    case trial::Status::Expired: MessageBox(nullptr, L"试用已到期。", L"提示", MB_ICONWARNING); return FALSE;
    case trial::Status::ExceedRuns: MessageBox(nullptr, L"试用次数已用尽。", L"提示", MB_ICONWARNING); return FALSE;
    case trial::Status::Debugger: MessageBox(nullptr, L"检测到调试环境，已退出。", L"安全", MB_ICONERROR); return FALSE;
    case trial::Status::Tampered: MessageBox(nullptr, L"检测到时间/数据异常，试用锁定。", L"安全", MB_ICONERROR); return FALSE;
    default: return FALSE;
    }
    // 继续初始化 UI...
    return TRUE;
}
```

---

## 6) 可选：服务端（PHP，PDO + SQLite/MySQL）

**路由：**

- `POST /trial/sync` 记录并返回 authoritative 状态
- `GET  /trial/time` 返回服务器UTC秒（用于时间基准）

**/trial/sync（精简示例）**

```php
<?php // trial_sync.php
// 输入：app_id, device_hash(base64), install_time, run_count, expire_time, nonce, hmac
// HMAC = HMAC-SHA256(APP_SECRET, canonical_json)
$APP_SECRET = hex2bin('2a915fc8...');
$pdo = new PDO('sqlite:/var/data/trial.db');
$pdo->exec("CREATE TABLE IF NOT EXISTS trial(device TEXT PRIMARY KEY, app_id TEXT, install_time INT, run_count INT, expire_time INT, updated_at INT)");

$raw = file_get_contents('php://input');
$req = json_decode($raw, true);
$canon = json_encode([$req['app_id'],$req['device_hash'],$req['install_time'],$req['run_count'],$req['expire_time']], JSON_UNESCAPED_SLASHES);
$h = hash_hmac('sha256', $canon, $APP_SECRET, true);
if (!hash_equals($h, base64_decode($req['hmac']))) { http_response_code(403); exit; }

$stmt = $pdo->prepare("INSERT INTO trial(device,app_id,install_time,run_count,expire_time,updated_at) VALUES(?,?,?,?,?,?)
                       ON CONFLICT(device) DO UPDATE SET run_count=max(run_count,excluded.run_count), expire_time=excluded.expire_time, updated_at=strftime('%s','now')");
$stmt->execute([$req['device_hash'],$req['app_id'],$req['install_time'],$req['run_count'],$req['expire_time'], time()]);

// 返回 authoritative 状态
$row = $pdo->query("SELECT run_count, expire_time FROM trial WHERE device=".$pdo->quote($req['device_hash']))->fetch(PDO::FETCH_ASSOC);
$now = time();
$status = 'OK';
if ($now > intval($row['expire_time'])) $status = 'Expired';
if (intval($row['run_count']) < intval($req['run_count'])) $status = 'Tampered'; // 本地回退

echo json_encode(['status'=>$status,'now'=>$now,'run_count'=>intval($row['run_count']),'expire_time'=>intval($row['expire_time'])]);
```

**/trial/time（极简）**

```php
<?php header('Content-Type: application/json'); echo json_encode(['now'=>time()]);
```

---

## 7) 构建与链接

- **VS 设置**：/std\:c++17；字符集 Unicode；启用 `/guard:cf`，启用 LTCG
- **链接库**：`bcrypt.lib`、`winhttp.lib`、`shlwapi.lib`
- **发布**：将 TrialGuard 编译为 DLL 或静态库；对导出函数做**代码虚拟化/混淆**（VMProtect/Themida等）

---

## 8) 安全加固清单（上线前务必执行）

-

---

## 9) 使用与扩展

- 本模板可满足**离线**场景；若担心**系统重装**绕过，开启 `/trial/sync`。
- 如需 **多产品共存**，将 `app_id` 纳入派生与存储键名，避免串扰。
- 如需**分级试用**（功能/时间/次数多维度），扩展 Doc 字段与 UI 提示策略。

> 提示：下一版可补充 **WMI 真·主板/磁盘指纹**、**WinHTTP 完整POST示例**、**RapidJSON 集成**、**更强的反调试** 与 **CI/CD 脚手架**。

