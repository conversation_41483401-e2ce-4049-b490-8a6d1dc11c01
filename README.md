# HALVault - 软件授权与试用管理系统

HALVault 是一个功能完整的 Windows 软件授权与试用管理 DLL 库，提供硬件指纹绑定、在线激活、试用期控制、序列号验证等核心功能。

## 🚀 核心特性

### 授权管理
- **硬件指纹绑定** - 基于 CPU、磁盘序列号、系统信息生成唯一 HWID
- **在线激活** - 支持服务器端授权验证和激活
- **序列号验证** - 内置序列号格式验证和解析
- **加密存储** - 使用 AES-256-GCM 加密保护授权文件

### 试用控制
- **多模式试用** - 支持按天数或按次数的试用限制
- **混合策略** - 先按次数后按天数的组合试用模式
- **防篡改** - 多副本存储、时间回拨检测、数据完整性校验
- **安全存储** - 注册表 + 文件 + NTFS ADS 三重备份

### 安全防护
- **反调试** - 检测调试器、虚拟机环境
- **代码完整性** - 自校验防止代码篡改
- **时间防护** - 防止系统时间回拨绕过试用期
- **进程保护** - 跨进程互斥锁防止并发冲突

## 📁 项目结构

```
HALVault/
├── halvault_api.h          # 主要 API 接口定义
├── halvault_error.h        # 错误码定义和处理
├── VaultManager.hpp        # 统一管理接口
├── hwid.hpp               # 硬件指纹相关
├── license.hpp            # 授权文件处理
├── activate.hpp           # 在线激活功能
├── SerialValidator.hpp    # 序列号验证
├── SoftwareCatalog.hpp    # 软件目录管理
├── AntiGuard.hpp          # 安全防护模块
├── HALVault/
│   ├── TrialManager.hpp   # 试用管理接口
│   ├── TrialManager.cpp   # 试用管理实现
│   └── trial_manager_*.cpp # 安全试用管理实现
├── doc/                   # 详细文档
└── code/                  # 辅助代码
```

## 🔧 编译环境

- **编译器**: Visual Studio 2019+ (v142 工具集)
- **标准**: C++17
- **平台**: Windows 7+ (x86/x64)
- **依赖库**:
  - bcrypt.lib (Windows CNG 加密)
  - winhttp.lib (HTTP 通信)
  - libcurl (网络请求)
  - cryptopp (加密算法)

## 📖 快速开始

### 1. 初始化系统

```cpp
#include "halvault_api.h"

// 初始化 HALVault
int result = HALVault_Initialize(L"YourSoftwareID", L"https://your-server.com");
if (result != HAL_OK) {
    // 处理初始化失败
}
```

### 2. 硬件指纹获取

```cpp
#include "VaultManager.hpp"

// 获取硬件指纹
wchar_t hwid[64];
int result = VaultManager::GetHWID(hwid, sizeof(hwid)/sizeof(wchar_t));
if (result == HAL_OK) {
    // hwid 包含 32 位十六进制字符串
}
```

### 3. 试用管理

```cpp
#include "HALVault/TrialManager.hpp"

// 初始化试用系统
int result = TrialInitialize("YourSoftwareID");

// 检查试用状态
TrialStatus status;
result = TrialEvaluate(status);
if (result == HAL_OK && status.allowed) {
    // 允许继续使用
    printf("剩余: %d %s\n", status.remaining, 
           status.mode == TrialMode::Days ? "天" : "次");
}

// 消耗一次使用次数（仅在按次数模式下）
result = TrialConsumeOne();
```

### 4. 在线激活

```cpp
#include "activate.hpp"

std::string licenseData;
int quantity, httpCode;

int result = RequestLicense(
    L"https://your-server.com",  // 服务器地址
    "your-api-key",              // API 密钥
    "SERIAL-NUMBER",             // 序列号
    hwidHex,                     // 硬件指纹
    "<EMAIL>",          // 联系方式
    1,                           // 软件 ID
    licenseData,                 // 返回的授权数据
    quantity,                    // 授权数量
    httpCode                     // HTTP 状态码
);
```

### 5. 授权验证

```cpp
#include "license.hpp"

LicenseInfo info;
int result = LoadLicenseEncrypted("license.dat", info);
if (result == HAL_OK) {
    // 检查授权是否有效
    uint32_t now = time(nullptr);
    if (now < info.expireUnix) {
        // 授权有效
    }
}
```

## 🛡️ 安全特性

### 试用保护机制
- **多副本存储**: 文件系统 + 注册表 + NTFS ADS 三重备份
- **加密保护**: AES-256-GCM 加密，绑定机器指纹
- **时间防护**: 检测系统时间回拨，防止试用期重置
- **完整性校验**: HMAC 签名验证数据完整性

### 反调试保护
- 检测调试器存在 (`IsDebuggerPresent`)
- 远程调试检测 (`CheckRemoteDebuggerPresent`)
- 时间扰动检测防止单步调试
- 虚拟机环境检测

### 代码保护
- 自校验机制防止代码篡改
- 控制流保护 (`/guard:cf`)
- 字符串加密（建议生产环境启用）
- 代码混淆支持（VMProtect/Themida）

## 🔌 API 参考

### 核心错误码

| 错误码 | 值 | 描述 |
|--------|----|----- |
| `HAL_OK` | 0 | 操作成功 |
| `HAL_ERR_FILE_NOT_FOUND` | 1 | 文件未找到 |
| `HAL_ERR_CRYPTO` | 4 | 加解密失败 |
| `HAL_ERR_HW_MISMATCH` | 5 | 硬件信息不匹配 |
| `HAL_ERR_LICENSE_EXPIRED` | 8 | 授权已过期 |

### 试用状态

```cpp
struct TrialStatus {
    bool       allowed;        // 是否允许继续试用
    uint32_t   remaining;      // 剩余天数/次数
    uint32_t   runsUsed;       // 已使用次数
    uint32_t   limit;          // 限制值
    TrialMode  mode;           // Days 或 Runs
    uint32_t   nowUnix;        // 当前时间戳
    uint32_t   installUnix;    // 安装时间戳
};
```

## 📋 使用场景

### 商业软件试用
- 提供 14-30 天的试用期
- 限制功能使用次数
- 防止重装系统绕过试用

### 企业授权管理
- 基于硬件指纹的设备绑定
- 在线激活和授权验证
- 支持批量授权管理

### 软件保护
- 防止逆向工程和破解
- 代码完整性保护
- 运行时环境检测

## 🔗 相关文档

- [试用管理接入指南](HALVault/trial_manager_接入指南（关键_api、时序与错误码）.md)
- [安全架构设计](doc/windows_试用时间与次数控制：安全架构与代码模板（v_1_）.md)

## 📄 许可证

本项目为商业软件保护解决方案，具体许可条款请联系开发团队。

## 🏗️ 架构设计

### 系统架构图

```mermaid
flowchart TB
    A[应用程序] --> B[HALVault API]
    B --> C[VaultManager 统一接口]
    C --> D[试用管理 TrialManager]
    C --> E[授权管理 License]
    C --> F[激活管理 Activate]
    C --> G[硬件指纹 HWID]
    C --> H[序列号验证 SerialValidator]
    C --> I[安全防护 AntiGuard]

    D --> J[多副本存储]
    J --> K[注册表]
    J --> L[文件系统]
    J --> M[NTFS ADS]

    E --> N[AES-256-GCM 加密]
    F --> O[HTTP/HTTPS 通信]
    G --> P[硬件信息采集]
    I --> Q[反调试检测]
```

### 数据流程

1. **初始化阶段**
   - 加载配置和密钥
   - 初始化加密模块
   - 建立安全上下文

2. **试用检查**
   - 读取多副本数据
   - 验证数据完整性
   - 执行时间和次数检查
   - 更新使用记录

3. **在线激活**
   - 生成激活请求
   - 发送到授权服务器
   - 验证服务器响应
   - 保存授权文件

### 存储策略

HALVault 采用三重存储策略确保数据安全：

1. **注册表存储** (`HKEY_CURRENT_USER`)
   - 路径: `Software\HALVault\{MD5(SoftwareID)}`
   - 优点: 系统集成度高，不易被普通用户发现
   - 缺点: 可能被注册表清理工具影响

2. **文件系统存储**
   - 路径: `%LocalAppData%\HALVault\{MD5}\trial.bin`
   - 路径: `%AppData%\HALVault\{MD5}\trial.bin`
   - 优点: 访问速度快，支持大容量数据
   - 缺点: 可能被文件清理工具删除

3. **NTFS ADS 存储**
   - 附加数据流: `trial.bin:alt`
   - 优点: 隐蔽性强，不占用目录空间
   - 缺点: 仅支持 NTFS 文件系统

### 加密机制

- **密钥派生**: HKDF-SHA256(APP_SECRET + machine_hash + salt)
- **加密算法**: AES-256-GCM (提供机密性和完整性)
- **随机数**: 12 字节 nonce，16 字节认证标签
- **附加认证数据**: 软件 ID 和版本信息

## 🔧 高级配置

### 试用策略配置

```cpp
// 获取默认配置
TrialMode mode;
uint32_t limit;
TrialGetDefaultConfig("VE-001", mode, limit);

// 自定义混合策略：先 5 次，后 7 天
TrialInitialize("VE-001", TrialMode::Days, 14);
TrialConfigureMixed(5, 7);
```

### 硬件指纹配置

```cpp
// 配置磁盘序列号获取策略
hwid::DiskFallback fallback = hwid::DiskFallback::VolumeSerial;
auto hwid = hwid::GetHWID(fallback);

// 获取特定驱动器信息
std::string diskSerial;
hwid::GetDiskSerialByLetter(L'C', diskSerial, fallback);
```

### 安全防护配置

```cpp
// 启动后台防护
anti::Start();

// 配置虚拟机检测
anti::SetVmAllowed(false);  // 禁止在虚拟机中运行

// 验证代码完整性
if (!anti::VerifySelfIntegrity()) {
    // 检测到代码被篡改
}
```

## 🚨 故障排除

### 常见问题

1. **初始化失败**
   - 检查 `bcrypt.lib` 是否正确链接
   - 确认 Windows 版本支持 CNG API
   - 验证软件 ID 格式是否正确

2. **试用数据丢失**
   - 检查用户权限是否足够
   - 确认磁盘空间是否充足
   - 验证杀毒软件是否误删文件

3. **硬件指纹不匹配**
   - 检查硬件是否发生变更
   - 确认磁盘序列号获取是否成功
   - 验证 WMI 服务是否正常

4. **网络激活失败**
   - 检查网络连接状态
   - 确认服务器地址是否正确
   - 验证 SSL 证书是否有效

### 调试技巧

```cpp
// 启用详细错误信息
int lastError = HalGetLastError();
const wchar_t* errorText = HalGetErrorText(lastError);

// 检查试用状态详情
TrialStatus status;
TrialEvaluate(status);
printf("Mode: %s, Remaining: %d, Used: %d\n",
       status.mode == TrialMode::Days ? "Days" : "Runs",
       status.remaining, status.runsUsed);
```

## 🤝 技术支持

如需技术支持或商业合作，请联系开发团队。

---

**注意**: 本文档基于当前代码结构生成，实际使用时请参考最新的 API 文档和示例代码。
