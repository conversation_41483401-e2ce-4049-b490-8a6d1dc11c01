
// Auto-generated by PolicyGen.py – DO NOT EDIT.
#pragma once
#include <cstddef>
constexpr uint32_t POLICY_VERSION = 124078282u;
constexpr unsigned char kPolicyKey[32] = { 0x56,0xE9,0xEF,0x6C,0x7F,0xD0,0x6B,0x64,0xE3,0x06,0xE7,0xCD,0x9C,0x3C,0xC0,0xC8,0xCB,0xB1,0xE3,0x3A,0x4F,0xDA,0x43,0x9E,0x9A,0x44,0x9F,0xC2,0xFD,0x6A,0x96,0x8E };
constexpr unsigned char kPolicyIV[16] = { 0x81,0x1D,0xBB,0xC1,0x03,0x17,0x3F,0x77,0xBE,0xD8,0x98,0x7C,0x80,0xD3,0x32,0xFE };
constexpr unsigned char kPolicyHmac[32] = { 0x6D,0x80,0xFA,0x2C,0x04,0xB6,0xBA,0x6A,0x42,0xD6,0x6C,0x60,0x85,0xEC,0x8D,0x9C,0x90,0xE4,0xAC,0x21,0xE6,0x71,0xFD,0x8D,0xAF,0x22,0x2C,0x3C,0x90,0xC1,0x13,0xA1 };
constexpr unsigned char kPolicyBlob[] = { 0x1D,0x02,0x3F,0x0D,0x78,0x2B,0xA2,0x1A,0x6B,0xF3,0xCD,0x3A,0x7E,0x3B,0x79,0x7D,0x3D,0xE4,0x30,0xFF,0xC3,0x94,0xE1,0x5F,0x8F,0x05,0x7E,0x73,0x96,0xC3,0x41,0xDC,0x9E,0x06,0x24,0xC0,0x59,0x24,0x14,0x03,0x49,0xA9,0x58,0x9F,0x65,0x6B,0xEB,0x70 };
constexpr size_t kPolicyBlobSize = sizeof(kPolicyBlob);
