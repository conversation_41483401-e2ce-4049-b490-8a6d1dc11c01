#pragma once
#include <cstdint>
#include <string>
#include <vector>
#include <array>
#include "halvault_api.h"
#include "halvault_error.h"

// 试用期模式
enum class TrialMode : uint8_t {
    Days = 0,    // 按天数限制
    Runs = 1     // 按运行次数限制
};

// 试用期状态
struct TrialState {
    uint32_t remaining;      // 剩余额度（天数或次数）
    uint32_t totalLimit;     // 总额度
    uint64_t nowUtc;         // 当前UTC时间戳
    uint64_t expireUtc;      // 过期UTC时间戳
    TrialMode mode;          // 当前模式
    bool isMixed;            // 是否为混合模式
    uint32_t runsRemaining;  // 混合模式下剩余次数
    uint32_t daysRemaining;  // 混合模式下剩余天数
};

// 试用期文档结构（内部使用）
struct TrialDocument {
    uint32_t schema;         // 数据格式版本
    uint64_t installTime;    // 首次安装时间（UTC秒）
    uint64_t expireTime;     // 试用到期时间（UTC秒）
    uint64_t lastRunTime;    // 最近一次成功运行（UTC秒）
    uint32_t runCount;       // 已运行次数
    uint32_t maxRuns;        // 最大允许次数
    uint32_t tamperCount;    // 可疑行为计数
    uint32_t seqNo;          // 序列号（用于检测回退）
    TrialMode mode;          // 试用模式
    bool isMixed;            // 是否为混合模式
    uint32_t mixedRunsFirst; // 混合模式：先用次数
    uint32_t mixedDaysAfter; // 混合模式：后用天数
    std::array<uint8_t, 32> machineHash; // 机器指纹哈希
    std::array<uint8_t, 16> salt;        // 随机盐
    std::string appId;       // 应用ID
    std::string version;     // 版本号
};

#ifdef __cplusplus
extern "C" {
#endif

// ========= 核心API =========

/**
 * 初始化试用期管理系统
 * @param softwareId 软件ID（必填）
 * @param mode 试用模式（Days或Runs）
 * @param limit 试用额度（天数或次数，0会被归一为1）
 * @return HAL_OK成功，其他值为错误码
 */
HALVAULT_API int TrialInitialize(const char* softwareId, TrialMode mode, uint32_t limit) noexcept;

/**
 * 查询当前剩余额度与总额度，不消耗次数
 * @param remaining 输出剩余额度（可为nullptr）
 * @param totalLimit 输出总额度（可为nullptr）
 * @return HAL_OK成功，HAL_E_TRIAL_EXPIRED过期，HAL_E_TRIAL_FROZEN冻结等
 */
HALVAULT_API int TrialEvaluate(uint32_t* remaining, uint32_t* totalLimit) noexcept;

/**
 * 在"按次阶段/模式"下消耗一次额度
 * @param remaining 输出剩余额度（可为nullptr）
 * @param totalLimit 输出总额度（可为nullptr）
 * @return 与TrialEvaluate相同；在"按天"阶段不会增加天数
 */
HALVAULT_API int TrialConsumeOneRun(uint32_t* remaining, uint32_t* totalLimit) noexcept;

/**
 * 获取默认配置
 * @param softwareId 软件ID
 * @param mode 输出试用模式
 * @param limit 输出试用额度
 * @return HAL_OK成功，其他值为错误码
 */
HALVAULT_API int TrialGetDefaultConfig(const char* softwareId, TrialMode& mode, uint32_t& limit) noexcept;

/**
 * 配置混合策略（先次数后天数）
 * @param runsFirst 先用次数
 * @param daysAfter 后用天数
 * @return HAL_OK成功，其他值为错误码
 * @note 需先调用TrialInitialize
 */
HALVAULT_API int TrialConfigureMixed(uint32_t runsFirst, uint32_t daysAfter) noexcept;

/**
 * 获取详细状态信息
 * @param state 输出状态信息
 * @return HAL_OK成功，其他值为错误码
 */
HALVAULT_API int TrialGetState(TrialState* state) noexcept;

/**
 * 重置试用期（仅用于开发测试）
 * @param softwareId 软件ID
 * @return HAL_OK成功，其他值为错误码
 */
HALVAULT_API int TrialReset(const char* softwareId) noexcept;

// ========= 存储位置管理 =========

/**
 * 设置存储根目录（用于移动存储设备）
 * @param rootPath 根目录路径（nullptr使用默认位置）
 * @return HAL_OK成功，其他值为错误码
 */
HALVAULT_API int TrialSetStorageRoot(const wchar_t* rootPath) noexcept;

/**
 * 获取当前存储根目录
 * @param buffer 输出缓冲区
 * @param bufferSize 缓冲区大小（字符数）
 * @return HAL_OK成功，其他值为错误码
 */
HALVAULT_API int TrialGetStorageRoot(wchar_t* buffer, size_t bufferSize) noexcept;

// ========= 诊断和维护 =========

/**
 * 验证试用期数据完整性
 * @param softwareId 软件ID
 * @return HAL_OK完整，其他值表示问题
 */
HALVAULT_API int TrialVerifyIntegrity(const char* softwareId) noexcept;

/**
 * 修复损坏的试用期数据
 * @param softwareId 软件ID
 * @return HAL_OK成功，其他值为错误码
 */
HALVAULT_API int TrialRepairData(const char* softwareId) noexcept;

/**
 * 获取试用期日志信息
 * @param buffer 输出缓冲区
 * @param bufferSize 缓冲区大小
 * @param actualSize 实际需要的大小
 * @return HAL_OK成功，其他值为错误码
 */
HALVAULT_API int TrialGetLog(char* buffer, size_t bufferSize, size_t* actualSize) noexcept;

#ifdef __cplusplus
}
#endif

// ========= C++接口（可选） =========

#ifdef __cplusplus
namespace trial {
    
    /**
     * C++包装类，提供RAII和异常安全
     */
    class Manager {
    public:
        Manager() = default;
        ~Manager() = default;
        
        // 禁止拷贝，允许移动
        Manager(const Manager&) = delete;
        Manager& operator=(const Manager&) = delete;
        Manager(Manager&&) = default;
        Manager& operator=(Manager&&) = default;
        
        /**
         * 初始化试用期管理
         */
        bool initialize(const std::string& softwareId, TrialMode mode, uint32_t limit);
        
        /**
         * 评估试用期状态
         */
        bool evaluate(uint32_t* remaining = nullptr, uint32_t* totalLimit = nullptr);
        
        /**
         * 消耗一次运行
         */
        bool consumeOneRun(uint32_t* remaining = nullptr, uint32_t* totalLimit = nullptr);
        
        /**
         * 获取详细状态
         */
        bool getState(TrialState& state);
        
        /**
         * 配置混合模式
         */
        bool configureMixed(uint32_t runsFirst, uint32_t daysAfter);
        
        /**
         * 获取最后错误码
         */
        int getLastError() const { return lastError_; }
        
    private:
        int lastError_ = HAL_OK;
    };
    
} // namespace trial
#endif

// ========= 实现部分（内联实现） =========

#ifdef HALVAULT_EXPORTS

#include "hwid.hpp"
#include <Windows.h>
#include <bcrypt.h>
#include <shlwapi.h>
#include <shlobj.h>
#include <mutex>
#include <chrono>
#include <cstring>

#pragma comment(lib, "bcrypt.lib")
#pragma comment(lib, "shlwapi.lib")
#pragma comment(lib, "shell32.lib")

namespace {
    // 编译期常量（生产环境应加密）
    constexpr char APP_SECRET[] = "\x2A\x91\x5F\xC8\x7B\x3E\x4D\x9A\x1C\x8F\x6E\x2B\x5A\x7D\x9C\x4E"
                                  "\x8B\x1F\x6A\x3C\x9E\x5D\x7B\x4A\x2F\x8C\x1E\x6D\x9A\x3B\x7E\x5C"; // 32字节

    constexpr wchar_t REG_BASE_PATH[] = L"Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced";
    constexpr uint32_t SCHEMA_VERSION = 1;
    constexpr uint32_t MAX_TAMPER_COUNT = 3;
    constexpr uint64_t TIME_DRIFT_THRESHOLD = 300; // 5分钟

    // 全局状态
    std::mutex g_mutex;
    bool g_initialized = false;
    TrialDocument g_document;
    std::string g_currentSoftwareId;

    // 机器指纹缓存
    std::array<uint8_t, 32> g_machineHash;
    bool g_machineHashValid = false;

    // 获取当前UTC时间戳
    uint64_t GetUtcNow() {
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::seconds>(duration).count();
    }

    // 生成随机字节
    bool GenerateRandomBytes(uint8_t* buffer, size_t size) {
        return SUCCEEDED(BCryptGenRandom(nullptr, buffer, static_cast<ULONG>(size),
                                       BCRYPT_USE_SYSTEM_PREFERRED_RNG));
    }

    // 获取机器指纹
    bool GetMachineFingerprint(std::array<uint8_t, 32>& hash) {
        if (g_machineHashValid) {
            hash = g_machineHash;
            return true;
        }

        try {
            // 使用现有的hwid模块获取硬件ID
            auto hwid = hwid::GetHWID();
            if (hwid.size() != 16) {
                return false;
            }

            // 获取额外的系统信息
            std::vector<uint8_t> fingerprint;
            fingerprint.insert(fingerprint.end(), hwid.begin(), hwid.end());

            // 添加卷序列号
            DWORD volumeSerial = 0;
            if (GetVolumeInformationW(L"C:\\", nullptr, 0, &volumeSerial, nullptr, nullptr, nullptr, 0)) {
                auto* ptr = reinterpret_cast<const uint8_t*>(&volumeSerial);
                fingerprint.insert(fingerprint.end(), ptr, ptr + sizeof(volumeSerial));
            }

            // 使用SHA256哈希
            BCRYPT_ALG_HANDLE hAlg = nullptr;
            if (!SUCCEEDED(BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_SHA256_ALGORITHM, nullptr, 0))) {
                return false;
            }

            BCRYPT_HASH_HANDLE hHash = nullptr;
            DWORD objectLength = 0, resultLength = 0;
            BCryptGetProperty(hAlg, BCRYPT_OBJECT_LENGTH, reinterpret_cast<PBYTE>(&objectLength),
                            sizeof(objectLength), &resultLength, 0);

            std::vector<uint8_t> hashObject(objectLength);
            if (!SUCCEEDED(BCryptCreateHash(hAlg, &hHash, hashObject.data(), objectLength, nullptr, 0, 0))) {
                BCryptCloseAlgorithmProvider(hAlg, 0);
                return false;
            }

            if (!SUCCEEDED(BCryptHashData(hHash, fingerprint.data(), static_cast<ULONG>(fingerprint.size()), 0))) {
                BCryptDestroyHash(hHash);
                BCryptCloseAlgorithmProvider(hAlg, 0);
                return false;
            }

            if (!SUCCEEDED(BCryptFinishHash(hHash, hash.data(), static_cast<ULONG>(hash.size()), 0))) {
                BCryptDestroyHash(hHash);
                BCryptCloseAlgorithmProvider(hAlg, 0);
                return false;
            }

            BCryptDestroyHash(hHash);
            BCryptCloseAlgorithmProvider(hAlg, 0);

            g_machineHash = hash;
            g_machineHashValid = true;
            return true;

        } catch (...) {
            return false;
        }
    }

    // 反调试检测
    bool IsDebugging() {
        if (IsDebuggerPresent()) return true;

        BOOL remoteDebugger = FALSE;
        if (CheckRemoteDebuggerPresent(GetCurrentProcess(), &remoteDebugger) && remoteDebugger) {
            return true;
        }

        return false;
    }

    // 简化的序列化
    bool SerializeDocument(const TrialDocument& doc, std::vector<uint8_t>& output) {
        output.clear();
        output.reserve(256); // 预分配空间

        auto writeData = [&](const void* data, size_t size) {
            const uint8_t* bytes = static_cast<const uint8_t*>(data);
            output.insert(output.end(), bytes, bytes + size);
        };

        writeData(&doc.schema, sizeof(doc.schema));
        writeData(&doc.installTime, sizeof(doc.installTime));
        writeData(&doc.expireTime, sizeof(doc.expireTime));
        writeData(&doc.lastRunTime, sizeof(doc.lastRunTime));
        writeData(&doc.runCount, sizeof(doc.runCount));
        writeData(&doc.maxRuns, sizeof(doc.maxRuns));
        writeData(&doc.tamperCount, sizeof(doc.tamperCount));
        writeData(&doc.seqNo, sizeof(doc.seqNo));
        writeData(&doc.mode, sizeof(doc.mode));
        writeData(&doc.isMixed, sizeof(doc.isMixed));
        writeData(doc.machineHash.data(), doc.machineHash.size());
        writeData(doc.salt.data(), doc.salt.size());

        uint32_t appIdLen = static_cast<uint32_t>(doc.appId.length());
        writeData(&appIdLen, sizeof(appIdLen));
        writeData(doc.appId.data(), doc.appId.length());

        uint32_t versionLen = static_cast<uint32_t>(doc.version.length());
        writeData(&versionLen, sizeof(versionLen));
        writeData(doc.version.data(), doc.version.length());

        return true;
    }

    // 简化的存储操作
    bool SaveTrialData(const std::string& softwareId, const std::vector<uint8_t>& data) {
        HKEY hKey = nullptr;
        if (RegCreateKeyExW(HKEY_CURRENT_USER, REG_BASE_PATH, 0, nullptr, 0, KEY_WRITE, nullptr, &hKey, nullptr) != ERROR_SUCCESS) {
            return false;
        }

        std::string hashInput = softwareId + "_trial";
        std::wstring valueName;
        valueName.reserve(hashInput.length());
        for (char c : hashInput) {
            valueName += static_cast<wchar_t>(c);
        }

        LONG result = RegSetValueExW(hKey, valueName.c_str(), 0, REG_BINARY, data.data(), static_cast<DWORD>(data.size()));
        RegCloseKey(hKey);

        return result == ERROR_SUCCESS;
    }

    // 简化的加密
    bool SimpleEncrypt(const std::vector<uint8_t>& plaintext, std::vector<uint8_t>& ciphertext) {
        ciphertext = plaintext;
        for (size_t i = 0; i < ciphertext.size(); ++i) {
            ciphertext[i] ^= APP_SECRET[i % (sizeof(APP_SECRET) - 1)];
        }
        return true;
    }
}

// ========= API实现 =========

extern "C" {

inline int TrialInitialize(const char* softwareId, TrialMode mode, uint32_t limit) noexcept {
    if (!softwareId || strlen(softwareId) == 0) {
        HalSetLastError(HAL_ERR_INVALID_ARG);
        return HAL_ERR_INVALID_ARG;
    }

    if (limit == 0) limit = 1;

    std::lock_guard<std::mutex> lock(g_mutex);

    try {
        // 反调试检测
        if (IsDebugging()) {
            HalSetLastError(HAL_E_TRIAL_DEBUGGER);
            return HAL_E_TRIAL_DEBUGGER;
        }

        g_currentSoftwareId = softwareId;

        // 创建新的试用期文档
        g_document = {};
        g_document.schema = SCHEMA_VERSION;
        g_document.installTime = GetUtcNow();
        g_document.expireTime = g_document.installTime + static_cast<uint64_t>(limit) * (mode == TrialMode::Days ? 86400 : 1);
        g_document.lastRunTime = g_document.installTime;
        g_document.runCount = 0;
        g_document.maxRuns = limit;
        g_document.tamperCount = 0;
        g_document.seqNo = 1;
        g_document.mode = mode;
        g_document.isMixed = false;
        g_document.mixedRunsFirst = 0;
        g_document.mixedDaysAfter = 0;
        g_document.appId = softwareId;
        g_document.version = "1.0.0";

        // 获取机器指纹
        if (!GetMachineFingerprint(g_document.machineHash)) {
            HalSetLastError(HAL_E_TRIAL_HWID_MISMATCH);
            return HAL_E_TRIAL_HWID_MISMATCH;
        }

        // 生成随机salt
        if (!GenerateRandomBytes(g_document.salt.data(), g_document.salt.size())) {
            HalSetLastError(HAL_E_TRIAL_CRYPTO);
            return HAL_E_TRIAL_CRYPTO;
        }

        // 序列化并保存
        std::vector<uint8_t> serialized;
        if (!SerializeDocument(g_document, serialized)) {
            HalSetLastError(HAL_E_TRIAL_IO);
            return HAL_E_TRIAL_IO;
        }

        std::vector<uint8_t> encrypted;
        if (!SimpleEncrypt(serialized, encrypted)) {
            HalSetLastError(HAL_E_TRIAL_CRYPTO);
            return HAL_E_TRIAL_CRYPTO;
        }

        // 保存到注册表
        if (!SaveTrialData(softwareId, encrypted)) {
            HalSetLastError(HAL_E_TRIAL_IO);
            return HAL_E_TRIAL_IO;
        }

        g_initialized = true;
        HalSetLastError(HAL_OK);
        return HAL_OK;

    } catch (...) {
        HalSetLastError(HAL_ERR_UNKNOWN);
        return HAL_ERR_UNKNOWN;
    }
}

inline int TrialEvaluate(uint32_t* remaining, uint32_t* totalLimit) noexcept {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (!g_initialized) {
        HalSetLastError(HAL_ERR_INVALID_ARG);
        return HAL_ERR_INVALID_ARG;
    }

    try {
        // 反调试检测
        if (IsDebugging()) {
            HalSetLastError(HAL_E_TRIAL_DEBUGGER);
            return HAL_E_TRIAL_DEBUGGER;
        }

        uint64_t now = GetUtcNow();

        // 时钟回拨检测
        if (now + TIME_DRIFT_THRESHOLD < g_document.lastRunTime) {
            g_document.tamperCount++;
            if (g_document.tamperCount > MAX_TAMPER_COUNT) {
                HalSetLastError(HAL_E_TRIAL_FROZEN);
                return HAL_E_TRIAL_FROZEN;
            }
        }

        uint32_t remainingValue = 0;
        uint32_t totalValue = g_document.maxRuns;

        if (g_document.mode == TrialMode::Days) {
            // 按天模式
            if (now > g_document.expireTime) {
                HalSetLastError(HAL_E_TRIAL_EXPIRED);
                return HAL_E_TRIAL_EXPIRED;
            }
            remainingValue = static_cast<uint32_t>((g_document.expireTime - now) / 86400);
        } else {
            // 按次模式
            if (g_document.runCount >= g_document.maxRuns) {
                HalSetLastError(HAL_E_TRIAL_EXPIRED);
                return HAL_E_TRIAL_EXPIRED;
            }
            remainingValue = g_document.maxRuns - g_document.runCount;
        }

        if (remaining) *remaining = remainingValue;
        if (totalLimit) *totalLimit = totalValue;

        HalSetLastError(HAL_OK);
        return HAL_OK;

    } catch (...) {
        HalSetLastError(HAL_ERR_UNKNOWN);
        return HAL_ERR_UNKNOWN;
    }
}

inline int TrialConsumeOneRun(uint32_t* remaining, uint32_t* totalLimit) noexcept {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (!g_initialized) {
        HalSetLastError(HAL_ERR_INVALID_ARG);
        return HAL_ERR_INVALID_ARG;
    }

    try {
        // 先评估当前状态
        int evalResult = TrialEvaluate(remaining, totalLimit);
        if (evalResult != HAL_OK) {
            return evalResult;
        }

        // 只在按次模式下消耗
        if (g_document.mode == TrialMode::Runs) {
            g_document.runCount++;
            g_document.seqNo++;
        }

        g_document.lastRunTime = GetUtcNow();

        // 保存更新后的数据
        std::vector<uint8_t> serialized;
        if (!SerializeDocument(g_document, serialized)) {
            HalSetLastError(HAL_E_TRIAL_IO);
            return HAL_E_TRIAL_IO;
        }

        std::vector<uint8_t> encrypted;
        if (!SimpleEncrypt(serialized, encrypted)) {
            HalSetLastError(HAL_E_TRIAL_CRYPTO);
            return HAL_E_TRIAL_CRYPTO;
        }

        if (!SaveTrialData(g_currentSoftwareId, encrypted)) {
            HalSetLastError(HAL_E_TRIAL_IO);
            return HAL_E_TRIAL_IO;
        }

        // 重新评估以获取更新后的剩余值
        return TrialEvaluate(remaining, totalLimit);

    } catch (...) {
        HalSetLastError(HAL_ERR_UNKNOWN);
        return HAL_ERR_UNKNOWN;
    }
}

inline int TrialGetDefaultConfig(const char* softwareId, TrialMode& mode, uint32_t& limit) noexcept {
    if (!softwareId) {
        HalSetLastError(HAL_ERR_INVALID_ARG);
        return HAL_ERR_INVALID_ARG;
    }

    std::string sid(softwareId);

    // 根据软件ID前缀设置默认配置
    if (sid.find("VE") == 0) {
        mode = TrialMode::Runs;
        limit = 30;
    } else if (sid.find("BDL") == 0) {
        mode = TrialMode::Days;
        limit = 7;
    } else {
        mode = TrialMode::Days;
        limit = 14;
    }

    HalSetLastError(HAL_OK);
    return HAL_OK;
}

} // extern "C"

#endif // HALVAULT_EXPORTS
