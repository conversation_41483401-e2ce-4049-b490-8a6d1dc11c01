# HALVault 项目完整技术文档

> 基于代码审查生成的完整项目文档 - Claude Code Assistant
> 生成时间: 2025-08-25

## 📋 目录

1. [项目概述](#项目概述)
2. [架构设计](#架构设计)
3. [核心模块详解](#核心模块详解)
4. [API 接口规范](#api-接口规范)
5. [数据结构与存储](#数据结构与存储)
6. [安全机制](#安全机制)
7. [编译与部署](#编译与部署)
8. [使用指南](#使用指南)
9. [故障排除](#故障排除)
10. [开发注意事项](#开发注意事项)

---

## 🏗️ 项目概述

### 基本信息

- **项目名称**: HALVault
- **项目类型**: Windows 软件授权与试用管理 DLL 库
- **开发语言**: C++17
- **目标平台**: Windows 7+ (x86/x64)
- **编译器**: Visual Studio 2019+ (v142 工具集)

### 核心功能

1. **软件授权管理**: 基于硬件指纹的设备绑定授权
2. **试用期控制**: 多模式试用限制（天数/次数/混合）
3. **在线激活**: 服务器端授权验证和激活
4. **序列号验证**: 内置序列号格式验证和解析
5. **安全防护**: 反调试、代码完整性、防篡改保护
6. **硬件指纹**: 基于 CPU、硬盘、系统信息生成唯一标识

### 技术特色

- **多重安全存储**: 注册表 + 文件系统 + NTFS ADS 三重备份
- **AES-256-GCM 加密**: 硬件绑定的加密保护
- **时间防护**: 检测系统时间回拨，防止试用期重置
- **完整性校验**: HMAC 签名验证数据完整性
- **跨进程同步**: 防止并发访问冲突

---

## 🏗️ 架构设计

### 系统层次结构

```
应用程序层
    ↓
HALVault API 层 (halvault_api.h)
    ↓
统一管理层 (VaultManager)
    ↓
核心功能模块层
├── 试用管理 (TrialManager)
├── 授权管理 (License)
├── 在线激活 (Activate)
├── 硬件指纹 (HWID)
├── 序列号验证 (SerialValidator)
├── 安全防护 (AntiGuard)
└── 软件目录 (SoftwareCatalog)
    ↓
存储与加密层
├── AES-256-GCM 加密
├── HKDF-SHA256 密钥派生
├── HMAC-SHA256 完整性校验
└── 多重存储策略
    ↓
系统 API 层
├── Windows CNG
├── WMI 查询
├── 设备管理器
├── WinHTTP/libcurl
└── 文件系统 API
```

### 核心模块关系图

```mermaid
graph TB
    subgraph "用户接口层"
        A[VaultManager 统一接口]
        B[halvault_api.h 核心 API]
    end
    
    subgraph "功能模块层"
        C[TrialManager 试用管理]
        D[License 授权管理]
        E[Activate 在线激活]
        F[HWID 硬件指纹]
        G[SerialValidator 序列号验证]
        H[AntiGuard 安全防护]
    end
    
    subgraph "存储层"
        I[注册表存储]
        J[文件系统存储]
        K[NTFS ADS 存储]
    end
    
    subgraph "网络层"
        L[HTTP/HTTPS 通信]
        M[JSON 数据处理]
    end
    
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    B --> A
    
    C --> I
    C --> J
    C --> K
    D --> I
    D --> J
    E --> L
    E --> M
    F --> I
```

---

## 🔧 核心模块详解

### 1. VaultManager - 统一管理接口

**文件**: `VaultManager.hpp`, `VaultManager.cpp`

**功能**:
- 提供统一的高级 API 接口
- 管理初始化和配置
- 路径解析和文件管理
- 错误处理和状态管理

**关键特性**:
- 线程安全的单例模式
- 自动路径解析（LocalAppData 优先）
- 统一的错误处理机制
- 支持自定义配置

**主要方法**:
```cpp
static void Initialize(projectName, companyName);
static int GetHWID(hwidHexBuf, bufLen);
static int CheckLicense(hasFile, valid);
static int ActivateOnline(serverUrl, serial, contact, ...);
static int GetLicenseInfo(info);
```

### 2. TrialManager - 试用管理核心

**文件**: `trialmanager.hpp`, `trialmanager.cpp`

**功能**:
- 安全的试用期控制
- 多模式试用策略
- 防篡改保护
- 时间回拨检测

**核心算法**:
- **多副本存储**: 6个独立存储位置，多数投票选择
- **AES-GCM 加密**: 绑定硬件指纹的加密保护
- **HMAC 验证**: 完整性校验防止数据篡改
- **时序检测**: 检测时间回拨和系统时钟异常

**存储策略**:
```cpp
// 主要副本
%LocalAppData%\HALVault\{MD5}\trial.bin
%AppData%\HALVault\{MD5}\trial.bin
HKCU\Software\HALVault\{MD5}\trial
HKCU\Software\HALVault\{MD5}\alt

// ADS 副本
trial.bin:alt
trial.bin:meta
```

### 3. HWID - 硬件指纹系统

**文件**: `hwid.hpp`, `hwid.cpp`

**功能**:
- 生成唯一硬件指纹
- 多种硬件信息采集
- 缓存和性能优化
- 故障回退机制

**硬件信息采集**:
- **CPU 信息**: `__cpuid` 获取 CPU 厂商标识
- **硬盘序列号**: DeviceIoControl + STORAGE_DEVICE_DESCRIPTOR
- **主板信息**: WMI 查询
- **系统信息**: Windows 版本和配置

**HWID 生成算法**:
```cpp
// 组合硬件信息
string data = cpuVendor + diskSerial + systemInfo;

// SHA-256 哈希并截取前 16 字节
array<uint8_t, 16> hwid = SHA256(data)[0:16];
```

### 4. License - 授权管理

**文件**: `license.hpp`, `license.cpp`

**功能**:
- 授权文件的加密存储
- 授权信息解析
- 过期检查
- 硬件绑定验证

**授权文件格式**:
```cpp
struct LicenseInfo {
    array<uint8_t, 16> hwid;      // 硬件指纹
    string contact;               // 联系方式
    uint8_t softwareId;           // 软件 ID
    string serial;                // 序列号
    uint32_t expireUnix;          // 过期时间
    uint32_t issuedAt;            // 签发时间
    uint8_t payloadVer;           // 载荷版本
    uint16_t quantity;            // 授权数量
    array<char, 8> serialPrefix;  // 序列号前缀
    string licenseStr;            // 原始授权字符串
};
```

### 5. Activate - 在线激活

**文件**: `activate.hpp`, `activate.cpp`

**功能**:
- 在线授权请求
- 服务器通信
- 授权状态检查
- 授权移除

**网络通信协议**:
- **协议**: HTTP/HTTPS
- **格式**: JSON
- **认证**: API Key + 请求签名
- **加密**: SSL/TLS

**API 端点**:
```
POST /api/activate    - 激活授权
POST /api/check       - 检查状态  
POST /api/remove      - 移除授权
```

### 6. SerialValidator - 序列号验证

**文件**: `SerialValidator.hpp`, `SerialValidator.cpp`

**功能**:
- 序列号格式验证
- 嵌入信息解析
- 联系方式验证
- 过期时间检查

**序列号格式**:
```
格式: XXXX-XXXX-XXXX-XXXX-XXXX
编码: Base32 编码的二进制数据
内容: 版本 + 软件ID + 过期时间 + 联系方式哈希 + 校验码
```

### 7. AntiGuard - 安全防护

**文件**: `AntiGuard.hpp`, `AntiGuard.cpp`

**功能**:
- 反调试保护
- 虚拟机检测
- 代码完整性验证
- 运行时保护

**保护机制**:
- **调试器检测**: `IsDebuggerPresent`, `CheckRemoteDebuggerPresent`
- **时间扰动**: 检测单步调试
- **虚拟机检测**: 检测 VMware, VirtualBox 等
- **代码校验**: 自校验防止代码篡改

---

## 🔌 API 接口规范

### 核心初始化 API

```cpp
// 系统初始化
int HALVault_Initialize(const wchar_t* softID, const wchar_t* serverUrl);

// 文件 MD5 计算
int HALVault_GetFileMD5(const wchar_t* pathOrNull, wchar_t* outMd5, size_t outMd5Chars);

// 文件验证
int HALVault_VerificationFile(const wchar_t* inMd5);
```

### 试用管理 API

```cpp
// 试用系统初始化
int TrialInitialize(const char* softwareId);

// 评估试用状态（不消耗）
int TrialEvaluate(uint32_t* remainingOut, uint32_t* totalLimitOut);

// 消耗一次使用（仅按次数模式）
int TrialConsumeOneRun(uint32_t* remainingOut, uint32_t* totalLimitOut);

// 获取默认配置
int TrialGetDefaultConfig(const char* softwareId, TrialMode& modeOut, uint32_t& limitOut);

// 配置混合策略
int TrialConfigureMixed(uint32_t runsFirst, uint32_t daysAfter);
```

### 硬件指纹 API

```cpp
namespace hwid {
    // 获取硬件指纹
    array<uint8_t, 16> GetHWID(DiskFallback fallback = DiskFallback::None);
    string GetHWIDHex(DiskFallback fallback = DiskFallback::None);
    
    // 磁盘序列号获取
    int GetDiskSerialByIndex(int index, string& outSerial, DiskFallback fallback);
    int GetDiskSerialByLetter(wchar_t driveLetter, string& outSerial, DiskFallback fallback);
    
    // 磁盘型号获取
    int GetDiskModelByIndex(int index, string& outModel);
    int GetDiskModelByLetter(wchar_t driveLetter, string& outModel);
    
    // 卷序列号获取
    int GetVolumeSerialByLetter(wchar_t driveLetter, string& outSerial);
    
    // USB 存储设备
    int GetUsbStorageSerials(vector<string>& serials);
}
```

### VaultManager 统一接口

```cpp
class VaultManager {
public:
    // 初始化
    static void Initialize(const wstring& projectName, const wstring& companyName);
    static void SetLicenseFilePath(const wstring& fullPath);
    
    // 错误处理
    static int GetLastError();
    static const wchar_t* GetErrorText(int code);
    static const wchar_t* GetLicenseFilePath();
    
    // 硬件指纹
    static int GetHWID(wchar_t* hwidHexBuf, size_t bufLen);
    
    // 授权检查
    static int CheckLicense(bool& hasFile, bool& valid);
    static int LicenseSecondsRemaining(uint32_t& seconds);
    
    // 序列号验证
    static int VerifySerial(const string& serial, const string& contact, SerialInfo& info);
    
    // 在线激活
    static int ActivateOnline(const wstring& serverUrl, const string& serial, 
                              const string& contact, const string& hwidHex, 
                              const int& softwareId, wchar_t* error, size_t bufLen, 
                              int& httpCode, bool ignoreCert = false);
    
    static int CheckOnline(const wstring& serverUrl, const string& serial,
                           const string& hwidHex, const int& softwareId,
                           wchar_t* error, size_t bufLen, int& httpCode, bool ignoreCert = false);
    
    static int RemoveOnline(const wstring& serverUrl, const string& serial,
                            const string& hwidHex, wchar_t* error, size_t bufLen,
                            int& httpCode, bool ignoreCert = false);
    
    // 授权信息获取
    static int GetLicenseInfo(LicenseInfo& info);
    static int GetLicenseInfoC(LicenseInfoC& info);
    static int GetLicenseExpiryDetails(uint32_t expireUnix, ExpiryDetails& details);
};
```

---

## 📊 数据结构与存储

### 试用数据结构

```cpp
struct TrialDocV2 {
    uint8_t  version;        // 版本号: 2
    uint8_t  mode;           // 试用模式: Days/Runs
    uint8_t  frozen;         // 冻结状态: 0/1
    uint8_t  reserved0;      // 保留字段（混合模式标志）
    uint32_t limit;          // 总限制（天数或次数）
    uint64_t installUnix;    // 首次安装时间（UTC 秒）
    uint64_t maxSeenUnix;    // 防回拨时间戳
    uint32_t daysAccrued;    // 单调递增天数计数器
    uint32_t lastDayIndex;   // 最后一次累积天数的日期索引
    uint32_t runsUsed;       // 已使用次数
    uint32_t seqNo;          // 单调递增序列号
    uint64_t reserved1;      // 保留字段（混合策略参数）
};
```

### 授权数据结构

```cpp
struct LicenseInfo {
    array<uint8_t, 16> hwid;      // 硬件指纹
    string contact;               // 联系方式
    uint8_t softwareId;           // 软件 ID
    string serial;                // 序列号
    uint32_t expireUnix;          // 过期时间（Unix 时间戳）
    uint32_t issuedAt;            // 签发时间
    uint8_t payloadVer;           // 载荷版本
    uint16_t quantity;            // 授权数量
    array<char, 8> serialPrefix;  // 序列号前缀
    string licenseStr;            // 原始授权字符串
};
```

### 存储路径策略

```cpp
// 试用数据存储位置
LocalAppData:  %LocalAppData%\HALVault\{MD5(SoftwareID)}\trial.bin
RoamingData:   %AppData%\HALVault\{MD5(SoftwareID)}\trial.bin
Registry1:     HKCU\Software\HALVault\{MD5(SoftwareID)}\trial
Registry2:     HKCU\Software\HALVault\{MD5(SoftwareID)}\alt
NTFS_ADS1:     trial.bin:alt
NTFS_ADS2:     trial.bin:meta

// 授权数据存储
LicenseFile:   %LocalAppData%\{Company}\{Project}\license.dat
               或自定义路径

// 日志存储
EvidenceLog:   %LocalAppData%\HALVault\{MD5(SoftwareID)}\trial.log
```

### 加密方案

```cpp
// 密钥派生
Key = SHA256(HWID + "HALVault" + SoftwareID)

// AES-GCM 加密
Encrypted = AES-256-GCM.Encrypt(
    Key: 派生密钥,
    IV: 12字节随机数,
    AAD: magic + version + SoftwareID_MD5[0:8],
    Plaintext: 序列化的试用数据
)

// 最终格式
File = Magic(4) + Version(1) + IV(12) + Encrypted + AuthTag(16)
```

---

## 🔐 安全机制

### 1. 加密保护

**对称加密**: AES-256-GCM
- **密钥**: 基于硬件指纹派生，设备唯一
- **认证**: 内建完整性检查，防篡改
- **随机性**: 每次加密使用新的 12 字节 IV

**密钥派生**: HKDF-SHA256
```cpp
BaseKey = SHA256(HWID + Salt + SoftwareID)
FinalKey = HKDF-SHA256(BaseKey, "HALVault-Trial", 32)
```

### 2. 完整性保护

**HMAC 签名**: HMAC-SHA256
```cpp
HMAC_Key = SHA256(BaseKey + "HALVault-LOGv1")
HMAC = HMAC-SHA256(HMAC_Key, Data + Version + SoftwareID)
```

**多数投票**: 6个副本中选择多数一致的数据
```cpp
Valid_Data = MajorityVote(All_Replicas)
```

### 3. 反篡改机制

**时间防护**:
- 检测系统时间回拨
- 单调递增的时间戳
- 时间异常时冻结试用

**证据链**:
- 追加写入的操作日志
- HMAC 签名的历史记录
- 异常行为检测和记录

### 4. 反调试保护

**静态检测**:
```cpp
bool IsDebuggerPresent();
bool CheckRemoteDebuggerPresent();
```

**动态检测**:
```cpp
// 时间扰动检测
auto start = GetTickCount();
// 执行一些操作
auto end = GetTickCount();
if (end - start > threshold) {
    // 可能存在调试器
}
```

**虚拟机检测**:
```cpp
bool IsRunningInVM() {
    // 检测虚拟机特征
    // - 硬件信息特征
    // - 进程名特征  
    // - 注册表特征
}
```

---

## 🛠️ 编译与部署

### 编译环境要求

**开发工具**:
- Visual Studio 2019 或更高版本
- Windows SDK 10.0 或更高版本
- C++17 标准支持

**依赖库**:
```cpp
// 系统库
#pragma comment(lib, "bcrypt.lib")      // Windows CNG 加密
#pragma comment(lib, "winhttp.lib")     // HTTP 通信
#pragma comment(lib, "setupapi.lib")    // 设备管理
#pragma comment(lib, "wbemuuid.lib")    // WMI 查询

// 第三方库
#pragma comment(lib, "cryptopp.lib")    // Crypto++ 加密库
#pragma comment(lib, "libcurl.lib")     // cURL 网络库 (可选)
```

### 编译配置

**预处理器定义**:
```cpp
// 编译 DLL 时定义
#define HALVAULT_EXPORTS

// 启用 Unicode
#define UNICODE
#define _UNICODE

// Windows 版本支持
#define _WIN32_WINNT 0x0601  // Windows 7+
```

**编译器选项**:
```
/std:c++17          // C++17 标准
/guard:cf           // 控制流保护
/GS                 // 缓冲区安全检查
/sdl                // SDL 检查
/W4                 // 警告等级 4
```

### 部署文件结构

```
YourApplication/
├── YourApp.exe
├── HALVault.dll         # 主要 DLL
├── HALVault.lib         # 导入库（开发时）
├── cryptopp.dll         # Crypto++ 运行时（如果动态链接）
├── libcurl.dll          # cURL 运行时（如果使用）
└── vcredist_x64.exe     # VC++ 运行时（如果需要）
```

---

## 📚 使用指南

### 1. 基本集成

**步骤 1: 包含头文件**
```cpp
#include "halvault_api.h"
#include "VaultManager.hpp"
#include "trialmanager.hpp"  // 如果使用试用功能
```

**步骤 2: 链接库文件**
```cpp
#pragma comment(lib, "HALVault.lib")
```

**步骤 3: 初始化系统**
```cpp
int main() {
    // 初始化 HALVault
    int result = HALVault_Initialize(L"MyApp-v1.0", L"https://license.myserver.com");
    if (result != HAL_OK) {
        printf("初始化失败: %d\n", HalGetLastError());
        return -1;
    }
    
    // 初始化 VaultManager
    VaultManager::Initialize(L"MyApplication", L"MyCompany");
    
    return 0;
}
```

### 2. 试用功能集成

```cpp
#include "trialmanager.hpp"

int CheckTrialStatus() {
    // 初始化试用系统
    int result = TrialInitialize("MyApp-v1.0");
    if (result != HAL_OK) {
        printf("试用初始化失败: %d\n", result);
        return result;
    }
    
    // 检查试用状态
    uint32_t remaining = 0, total = 0;
    result = TrialEvaluate(&remaining, &total);
    
    switch (result) {
    case HAL_OK:
        printf("试用有效，剩余: %d\n", remaining);
        break;
    case HAL_E_TRIAL_EXPIRED:
        printf("试用已过期\n");
        return result;
    case HAL_E_TRIAL_FROZEN:
        printf("试用被冻结（检测到时间异常）\n");
        return result;
    default:
        printf("试用检查失败: %d\n", result);
        return result;
    }
    
    return HAL_OK;
}

// 在需要消耗次数的地方调用（仅按次数模式）
int ConsumeUsage() {
    uint32_t remaining = 0, total = 0;
    int result = TrialConsumeOneRun(&remaining, &total);
    
    if (result == HAL_OK) {
        printf("使用次数消耗成功，剩余: %d/%d\n", remaining, total);
    }
    
    return result;
}
```

### 3. 硬件指纹获取

```cpp
#include "VaultManager.hpp"

void GetHardwareFingerprint() {
    wchar_t hwid[64] = {0};
    int result = VaultManager::GetHWID(hwid, sizeof(hwid)/sizeof(wchar_t));
    
    if (result == HAL_OK) {
        wprintf(L"硬件指纹: %s\n", hwid);
    } else {
        printf("获取硬件指纹失败: %d\n", VaultManager::GetLastError());
    }
}
```

### 4. 在线激活

```cpp
#include "VaultManager.hpp"

int ActivateLicense(const std::string& serial, const std::string& contact) {
    // 获取硬件指纹
    wchar_t hwid[64] = {0};
    int result = VaultManager::GetHWID(hwid, sizeof(hwid)/sizeof(wchar_t));
    if (result != HAL_OK) {
        return result;
    }
    
    // 转换为字符串
    std::string hwidStr;
    // ... 转换逻辑
    
    // 执行在线激活
    wchar_t error[512] = {0};
    int httpCode = 0;
    
    result = VaultManager::ActivateOnline(
        L"https://your-server.com",  // 服务器地址
        serial,                      // 序列号
        contact,                     // 联系方式
        hwidStr,                     // 硬件指纹
        1,                          // 软件 ID
        error, sizeof(error)/sizeof(wchar_t),
        httpCode
    );
    
    if (result == HAL_OK) {
        printf("激活成功\n");
    } else {
        wprintf(L"激活失败: %s (HTTP: %d)\n", error, httpCode);
    }
    
    return result;
}
```

### 5. 授权检查

```cpp
#include "VaultManager.hpp"

int CheckLicenseStatus() {
    bool hasFile = false, valid = false;
    int result = VaultManager::CheckLicense(hasFile, valid);
    
    if (result == HAL_OK) {
        if (hasFile && valid) {
            // 获取授权详细信息
            LicenseInfo info;
            result = VaultManager::GetLicenseInfo(info);
            if (result == HAL_OK) {
                printf("授权有效，过期时间: %u\n", info.expireUnix);
                
                // 检查剩余时间
                uint32_t remainingSeconds = 0;
                result = VaultManager::LicenseSecondsRemaining(remainingSeconds);
                if (result == HAL_OK) {
                    printf("剩余时间: %u 秒\n", remainingSeconds);
                }
            }
        } else {
            printf("授权文件不存在或无效\n");
        }
    } else {
        printf("授权检查失败: %d\n", result);
    }
    
    return result;
}
```

---

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 初始化失败

**症状**: `HALVault_Initialize` 返回非零值

**可能原因**:
- 缺少 VC++ 运行时
- 缺少 Windows SDK
- 权限不足

**解决方案**:
```cpp
int result = HALVault_Initialize(L"MyApp", L"https://server.com");
if (result != HAL_OK) {
    int error = HalGetLastError();
    const wchar_t* errorText = HalGetErrorText(error);
    wprintf(L"初始化失败: %s (代码: %d)\n", errorText, error);
    
    // 检查具体错误
    switch (error) {
    case HAL_ERR_CRYPTO:
        printf("加密模块初始化失败，检查 CNG 支持\n");
        break;
    case HAL_ERR_INVALID_ARG:
        printf("参数无效，检查软件 ID 格式\n");
        break;
    // ... 其他错误处理
    }
}
```

#### 2. 试用数据丢失

**症状**: 试用期被重置

**可能原因**:
- 文件被删除
- 注册表被清理
- 权限不足

**调试方法**:
```cpp
// 检查存储位置的可访问性
std::wstring licensePath = VaultManager::GetLicenseFilePath();
DWORD attributes = GetFileAttributes(licensePath.c_str());
if (attributes == INVALID_FILE_ATTRIBUTES) {
    printf("授权文件不存在: %ls\n", licensePath.c_str());
}

// 检查注册表访问权限
HKEY hKey;
LSTATUS status = RegOpenKeyEx(HKEY_CURRENT_USER, 
    L"Software\\HALVault", 0, KEY_READ, &hKey);
if (status != ERROR_SUCCESS) {
    printf("注册表访问失败: %d\n", status);
} else {
    RegCloseKey(hKey);
}
```

#### 3. 硬件指纹不匹配

**症状**: 授权文件在同一台机器上失效

**可能原因**:
- 硬件配置变更
- 驱动程序更新
- 磁盘序列号获取失败

**调试方法**:
```cpp
#include "hwid.hpp"

void DiagnoseHWID() {
    // 检查各个硬件组件
    auto hwid = hwid::GetHWID();
    printf("当前 HWID: ");
    for (uint8_t b : hwid) {
        printf("%02X", b);
    }
    printf("\n");
    
    // 检查磁盘序列号
    std::string diskSerial;
    int result = hwid::GetDiskSerialByLetter(L'C', diskSerial);
    if (result == HAL_OK) {
        printf("C 盘序列号: %s\n", diskSerial.c_str());
    } else {
        printf("无法获取 C 盘序列号，错误: %d\n", result);
        
        // 尝试使用卷序列号作为备用
        result = hwid::GetDiskSerialByLetter(L'C', diskSerial, hwid::DiskFallback::VolumeSerial);
        if (result == HAL_OK) {
            printf("使用卷序列号: %s\n", diskSerial.c_str());
        }
    }
}
```

#### 4. 网络激活失败

**症状**: 在线激活返回网络错误

**可能原因**:
- 网络连接问题
- 服务器地址错误
- SSL 证书验证失败
- 防火墙阻止

**调试方法**:
```cpp
int DebugActivation(const std::string& serial, const std::string& contact) {
    wchar_t hwid[64] = {0};
    VaultManager::GetHWID(hwid, sizeof(hwid)/sizeof(wchar_t));
    
    wchar_t error[1024] = {0};
    int httpCode = 0;
    
    // 先尝试忽略 SSL 证书验证
    int result = VaultManager::ActivateOnline(
        L"https://your-server.com",
        serial, contact, /* hwid string */, 1,
        error, sizeof(error)/sizeof(wchar_t),
        httpCode,
        true  // 忽略证书验证
    );
    
    printf("HTTP 状态码: %d\n", httpCode);
    wprintf(L"服务器响应: %s\n", error);
    
    if (httpCode == 0) {
        printf("网络连接失败，检查:\n");
        printf("1. 网络连接状态\n");
        printf("2. 防火墙设置\n");
        printf("3. 代理配置\n");
    } else if (httpCode >= 400) {
        printf("服务器返回错误，检查:\n");
        printf("1. 服务器地址是否正确\n");
        printf("2. API 密钥是否有效\n");
        printf("3. 序列号格式是否正确\n");
    }
    
    return result;
}
```

### 调试技巧

#### 1. 启用详细日志

```cpp
// 在调试版本中添加详细日志
#ifdef _DEBUG
    #define HAL_DEBUG_LOG(fmt, ...) printf("[HAL] " fmt "\n", __VA_ARGS__)
#else
    #define HAL_DEBUG_LOG(fmt, ...)
#endif

// 使用示例
HAL_DEBUG_LOG("HWID: %s", hwidString.c_str());
HAL_DEBUG_LOG("Trial remaining: %d", remaining);
```

#### 2. 错误码映射

```cpp
const char* GetErrorDescription(int errorCode) {
    switch (errorCode) {
    case HAL_OK: return "操作成功";
    case HAL_ERR_FILE_NOT_FOUND: return "文件未找到";
    case HAL_ERR_FORMAT: return "文件格式错误";
    case HAL_ERR_CRC: return "校验失败";
    case HAL_ERR_CRYPTO: return "加解密失败";
    case HAL_ERR_HW_MISMATCH: return "硬件信息不匹配";
    case HAL_ERR_HTTP: return "HTTP 请求失败";
    case HAL_ERR_JSON: return "JSON 解析失败";
    case HAL_ERR_VERIFY_FAIL: return "验证失败";
    case HAL_ERR_VERIFY_CONTACT: return "需要联系支持";
    case HAL_ERR_DISK_IO: return "磁盘 IO 错误";
    case HAL_ERR_INVALID_ARG: return "非法参数";
    case HAL_ERR_LICENSE_EXPIRED: return "授权过期";
    case HAL_ERR_CURLE_FAILED_INIT: return "CURL 初始化失败";
    default: return "未知错误";
    }
}
```

---

## ⚠️ 开发注意事项

### 1. 安全考虑

**密钥管理**:
- 绝不在代码中硬编码敏感信息
- 使用安全的密钥派生方法
- 定期更新加密算法和密钥

**数据保护**:
- 所有敏感数据必须加密存储
- 使用安全的内存清零方法
- 避免在日志中记录敏感信息

```cpp
// 安全清零内存
void SecureZeroMemory(void* ptr, size_t size) {
    volatile char* p = static_cast<volatile char*>(ptr);
    for (size_t i = 0; i < size; ++i) {
        p[i] = 0;
    }
}

// 使用示例
char password[256];
// ... 使用密码
SecureZeroMemory(password, sizeof(password));
```

### 2. 性能优化

**缓存策略**:
- 硬件指纹计算结果缓存
- 授权状态检查结果缓存
- 磁盘信息查询缓存

**异步处理**:
- 网络请求使用异步模式
- 大文件操作使用后台线程
- UI 操作与耗时操作分离

### 3. 兼容性考虑

**操作系统版本**:
- 支持 Windows 7 及以上版本
- 处理不同版本的 API 差异
- 优雅降级不支持的功能

**硬件兼容性**:
- 处理无硬盘序列号的情况
- 支持虚拟磁盘和网络驱动器
- 处理硬件信息获取失败

### 4. 错误处理最佳实践

**异常安全**:
- 使用 RAII 管理资源
- 异常安全的函数设计
- 适当的异常处理策略

```cpp
// RAII 资源管理示例
class FileHandle {
private:
    HANDLE handle;
public:
    FileHandle(const wchar_t* filename) 
        : handle(CreateFile(filename, ...)) {
        if (handle == INVALID_HANDLE_VALUE) {
            throw std::runtime_error("文件打开失败");
        }
    }
    
    ~FileHandle() {
        if (handle != INVALID_HANDLE_VALUE) {
            CloseHandle(handle);
        }
    }
    
    HANDLE get() const { return handle; }
};
```

**错误传播**:
- 使用错误码而非异常
- 提供详细的错误信息
- 保持错误处理的一致性

### 5. 测试策略

**单元测试**:
- 核心算法的单元测试
- 边界条件测试
- 错误处理测试

**集成测试**:
- 多模块协作测试
- 不同环境兼容性测试
- 性能压力测试

**安全测试**:
- 反调试绕过测试
- 数据篡改检测测试
- 时间回拨模拟测试

---

## 📝 版本历史与更新

### 当前已知问题

基于代码审查发现的问题：

1. **hwid.cpp**:
   - ✅ 缓冲区管理错误（已修复）
   - ⚠️ 线程安全问题需要注意
   - ⚠️ 内存边界检查可以加强

2. **VaultManager.cpp**:
   - ✅ 语法错误已修复
   - ✅ 错误处理逻辑已优化
   - ✅ 缓冲区检查已加强

3. **trialmanager.cpp**:
   - ✅ 策略表功能已实现
   - ✅ 加密逻辑已完善
   - ✅ 中文注释编码问题已解决

### 建议的改进方向

1. **性能优化**:
   - 实现更智能的缓存策略
   - 优化网络请求的并发处理
   - 减少不必要的系统调用

2. **安全加强**:
   - 添加代码混淆支持
   - 实现反内存扫描保护
   - 加强虚拟机检测能力

3. **功能扩展**:
   - 支持云端策略配置
   - 添加更多硬件指纹源
   - 实现离线激活功能

---

**文档更新**: 本文档将根据代码变更持续更新，确保与实际实现保持同步。

**技术支持**: 如有问题或建议，请联系开发团队或提交 Issue。